#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
简单测试群名修改功能
"""

import sys
sys.path.insert(0, '.')

try:
    from PyOfficeRobot.core.monitor.group_monitor import GroupMonitor
    print("✅ GroupMonitor 导入成功")
    
    # 创建监控器实例
    monitor = GroupMonitor(
        enable_ai_analysis=False,
        enable_data_storage=False,
        my_username="测试用户",
        candidate_group_names=["test_demo1", "test_demo2"]
    )
    print("✅ GroupMonitor 实例创建成功")
    
    # 测试群名修改解析
    test_content = '"凡梦"修改群名为"test_demo2"'
    print(f"📝 测试群名修改消息: {test_content}")
    
    # 手动测试解析逻辑
    import re
    patterns = [
        r'修改群名为"([^"]+)"',  # 标准格式：修改群名为"群名"
        r'修改群名为"([^"]*)"',  # 允许空群名
        r'修改群名为(.+)$',      # 没有引号的格式
        r'群名为"([^"]+)"',      # 简化格式：群名为"群名"
        r'"([^"]*)"修改群名为"([^"]*)"',  # 完整格式："用户"修改群名为"群名"
    ]
    
    new_group_name = None
    for pattern in patterns:
        match = re.search(pattern, test_content)
        if match:
            if pattern == r'"([^"]*)"修改群名为"([^"]*)"':
                # 对于完整格式，取第二个匹配组（群名）
                new_group_name = match.group(2).strip()
            else:
                # 对于其他格式，取第一个匹配组
                new_group_name = match.group(1).strip()
            # 移除可能的引号和其他特殊字符
            new_group_name = new_group_name.strip('"\'`""''')
            # 如果提取的群名不为空，跳出循环
            if new_group_name:
                print(f"✅ 成功解析新群名: '{new_group_name}' (使用模式: {pattern})")
                break
            else:
                print(f"解析到空群名，尝试下一个模式")
    
    if new_group_name:
        print(f"🎯 解析结果: '{new_group_name}'")
    else:
        print("❌ 无法解析群名")
    
    print("🎉 简单测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()