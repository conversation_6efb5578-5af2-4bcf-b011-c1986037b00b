# -*- coding: UTF-8 -*-
'''
群聊监控基础功能演示
'''

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

import PyOfficeRobot

def message_handler(message):
    """消息处理器示例"""
    print(f"收到消息: [{message.sender}] {message.content}")
    
    # 这里可以添加自定义的消息处理逻辑
    if message.message_type.value == "text":
        print(f"  -> 文本消息: {message.content}")
    elif message.message_type.value == "image":
        print(f"  -> 图片消息")
    elif message.message_type.value == "file":
        print(f"  -> 文件消息: {message.content}")

def main():
    """主函数"""
    print("🚀 群聊监控基础功能演示")
    print("=" * 40)
    
    # 创建监控器
    print("正在创建监控器...")
    monitor = PyOfficeRobot.group.create_group_monitor()
    print("✅ 监控器创建成功")
    
    # 添加消息处理器
    monitor.add_message_handler(message_handler)
    print("✅ 消息处理器添加成功")
    
    # 开始监控指定群聊
    group_name = "test_demo1"  # 请替换为实际的群聊名称
    print(f"📱 开始监控群聊: {group_name}")
    print("💡 按 Ctrl+C 停止监控")
    print("-" * 40)
    
    try:
        monitor.start_monitoring(group_name, check_interval=3)
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")
    except Exception as e:
        print(f"❌ 监控出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()