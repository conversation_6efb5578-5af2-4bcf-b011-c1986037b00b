#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试简化版群名修改处理功能
验证只检测关键词"修改群名为"就启动群名更换操作
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

from PyOfficeRobot.core.monitor.group_monitor import GroupMonitor
from PyOfficeRobot.core.monitor.models import Message, MessageType

def test_simplified_group_name_change():
    """测试简化版群名修改处理功能"""
    print("🧪 测试简化版群名修改处理功能")
    print("=" * 60)
    
    # 1. 创建监控器实例
    print("📋 步骤1: 创建监控器实例")
    candidate_groups = ["test_demo1", "test_demo2", "浪前 6.1-运营官交流群（全员）"]
    monitor = GroupMonitor(
        enable_ai_analysis=False,
        enable_data_storage=True,
        storage_path="./test_supervisor",
        my_username="The end",
        enable_qa=True,
        enable_violation_warning=True,
        candidate_group_names=candidate_groups
    )
    
    print(f"✅ 监控器初始化完成")
    print(f"📋 候选群聊列表: {monitor.candidate_group_names}")
    print(f"🎯 当前群聊: {monitor.current_group}")
    
    # 2. 设置初始状态
    print("\n📋 步骤2: 设置初始状态")
    monitor.current_group = "test_demo1"
    print(f"📝 设置当前群聊: {monitor.current_group}")
    print(f"🔄 群名修改状态: {monitor.is_group_name_changing}")
    print(f"📤 消息队列大小: {len(monitor.message_queue)}")
    
    # 3. 测试群名修改消息检测
    print("\n🔄 步骤3: 测试群名修改消息检测")
    
    test_messages = [
        '"凡梦"修改群名为"test_demo2"',
        '"张三"修改群名为"新群名"',
        '用户修改群名为"另一个群名"',
        '修改群名为"简单群名"',
        '群名已修改群名为"测试群"',
        '这不是修改群名的消息',  # 这个不应该触发
    ]
    
    for i, content in enumerate(test_messages, 1):
        print(f"\n  测试消息 {i}: {content}")
        
        # 检查是否包含关键词
        if "修改群名为" in content:
            print(f"    ✅ 包含关键词 '修改群名为'，会触发群名修改处理")
            
            # 创建系统消息
            message = Message(
                id=f"test_msg_{i:03d}",
                sender="SYS",
                content=content,
                message_type=MessageType.SYSTEM,
                timestamp=datetime.now(),
                group_name=monitor.current_group,
                raw_data=("SYS", content, f"test_msg_{i:03d}")
            )
            
            # 模拟处理（不实际执行，只显示逻辑）
            print(f"    🔧 模拟处理群名修改消息...")
            print(f"    📝 当前群聊: {monitor.current_group}")
            print(f"    🔄 将设置群名修改状态为 True")
            print(f"    📤 将暂停QA和违规检测功能")
            print(f"    🔍 将在候选群聊中查找可用群聊")
            print(f"    ✅ 处理完成后恢复正常功能")
            
        else:
            print(f"    ❌ 不包含关键词 '修改群名为'，不会触发处理")
    
    # 4. 测试消息队列功能
    print("\n📤 步骤4: 测试消息队列功能")
    
    # 模拟开始群名修改过程
    print(f"🔄 模拟开始群名修改过程...")
    monitor._start_group_name_change_process()
    
    print(f"📊 群名修改状态:")
    print(f"    群名修改中: {monitor.is_group_name_changing}")
    print(f"    开始时间: {monitor.group_change_start_time}")
    print(f"    消息队列大小: {len(monitor.message_queue)}")
    
    # 模拟群名修改期间的消息
    queued_messages = [
        Message(
            id="queued_001",
            sender="用户A",
            content="这条消息会被加入队列",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户A", "这条消息会被加入队列", "queued_001")
        ),
        Message(
            id="queued_002",
            sender="用户B",
            content="@The end 这个问答也会被队列",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户B", "@The end 这个问答也会被队列", "queued_002")
        ),
        Message(
            id="queued_003",
            sender="用户C",
            content="加我微信",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户C", "加我微信", "queued_003")
        )
    ]
    
    print(f"\n📝 模拟 {len(queued_messages)} 条消息在群名修改期间到达:")
    for i, message in enumerate(queued_messages, 1):
        print(f"  消息 {i}: [{message.sender}] {message.content}")
        
        # 模拟消息处理逻辑
        if monitor.is_group_name_changing:
            print(f"    🔄 群名修改中，消息加入队列")
            monitor.message_queue.append(message)
        else:
            print(f"    ✅ 正常处理模式")
    
    print(f"\n📊 队列状态:")
    print(f"    消息队列大小: {len(monitor.message_queue)}")
    for i, msg in enumerate(monitor.message_queue, 1):
        print(f"      {i}. [{msg.sender}] {msg.content}")
    
    # 5. 测试群名修改完成
    print("\n✅ 步骤5: 测试群名修改完成")
    
    # 模拟群名修改完成
    monitor.current_group = "test_demo2"  # 假设重新连接到了这个群聊
    print(f"📝 模拟重新连接成功，当前群聊: {monitor.current_group}")
    
    # 结束群名修改过程
    print(f"🔄 结束群名修改过程，处理队列消息...")
    monitor._end_group_name_change_process()
    
    print(f"📊 处理完成后的状态:")
    print(f"    群名修改中: {monitor.is_group_name_changing}")
    print(f"    消息队列大小: {len(monitor.message_queue)}")
    print(f"    当前群聊: {monitor.current_group}")
    
    # 6. 测试队列状态查询
    print("\n📊 步骤6: 测试队列状态查询")
    
    queue_status = monitor.get_queue_status()
    print(f"📝 队列状态信息:")
    for key, value in queue_status.items():
        print(f"    {key}: {value}")
    
    # 7. 总结测试结果
    print("\n📊 测试总结:")
    print(f"   当前群聊: {monitor.current_group}")
    print(f"   候选群聊数量: {len(monitor.candidate_group_names)}")
    print(f"   群名修改状态: {monitor.is_group_name_changing}")
    print(f"   消息队列大小: {len(monitor.message_queue)}")
    
    print("\n🎉 简化版群名修改处理功能测试完成")
    print("=" * 60)
    
    return {
        "current_group": monitor.current_group,
        "is_group_name_changing": monitor.is_group_name_changing,
        "queue_size": len(monitor.message_queue),
        "test_passed": True
    }

def demonstrate_simplified_approach():
    """演示简化方法的优势"""
    print("\n🔧 演示简化方法的优势")
    print("=" * 40)
    
    print("❌ 复杂正则表达式方法的问题:")
    print("   1. 正则表达式匹配失败，无法解析群名")
    print("   2. 需要处理各种引号和特殊字符")
    print("   3. 不同的消息格式需要不同的模式")
    print("   4. 解析出的群名可能不在候选列表中")
    print("   5. 代码复杂，容易出错")
    
    print("\n✅ 简化关键词检测方法的优势:")
    print("   1. 只检测 '修改群名为' 关键词，简单可靠")
    print("   2. 不需要解析具体的新群名")
    print("   3. 直接使用现有候选群聊列表重新连接")
    print("   4. 代码简洁，逻辑清晰")
    print("   5. 避免了复杂的字符串处理")
    
    print("\n🛠️ 核心实现逻辑:")
    print("   1. 检测到 '修改群名为' 关键词")
    print("   2. 设置群名修改状态，暂停其他功能")
    print("   3. 在候选群聊列表中查找可用群聊")
    print("   4. 重新连接到找到的群聊")
    print("   5. 恢复正常功能，处理队列消息")
    
    print("\n📊 处理流程对比:")
    print("   复杂方法: 检测 -> 解析群名 -> 验证群名 -> 重连")
    print("   简化方法: 检测 -> 直接重连 -> 完成")
    
    print("\n🎯 适用场景:")
    print("   ✅ 候选群聊列表已知且稳定")
    print("   ✅ 群名修改后仍在候选列表中")
    print("   ✅ 重连成功率高")
    print("   ✅ 对具体新群名不敏感")

if __name__ == "__main__":
    try:
        # 运行功能测试
        result = test_simplified_group_name_change()
        
        # 演示简化方法
        demonstrate_simplified_approach()
        
        print(f"\n🎯 测试结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()