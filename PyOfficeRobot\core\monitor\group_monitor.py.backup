# -*- coding: UTF-8 -*-
"""
群聊监控主控制器
"""

import time
from typing import List, Optional, Callable
from datetime import datetime
from loguru import logger

from ..WeChatType import WeChat
from .models import Message, MessageType, ViolationRecord
from .zhipu_analyzer import <PERSON>hipuAnaly<PERSON>, AnalysisResult
from .qa_handler import QAHandler


class GroupMonitor:
    """群聊监控主控制器"""
    
    def __init__(self, zhipu_api_key: str = None, enable_ai_analysis: bool = False, 
                 storage_path: str = "./monitor_data", enable_data_storage: bool = True,
                 my_username: str = None, enable_qa: bool = False, enable_violation_warning: bool = True,
                 candidate_group_names: list = None):
        """初始化监控器"""
        self.wx = WeChat()
        self.is_monitoring = False
        self.current_group = None
        self.last_processed_message_id = None
        self.message_handlers = []
        self.violation_records = []
        
        # 用户名设置（用于过滤自己的消息）
        self.my_username = my_username
        
        # 候选群聊名称列表
        self.candidate_group_names = candidate_group_names or [
            "test_demo1",
            "test_demo2",
            "浪前 6.1-运营官交流群（全员）",
            "富足社群-周周",
            "浪前 6.1",
            "浪前6.1-运营官",
            "浪前6.1-运营官交流群",
            "测试群",
            "工作群",
            "学习群"
        ]
        
        # 消息查重机制
        self.processed_message_ids = set()  # 已处理的消息ID集合
        self.recent_qa_questions = {}  # 最近的问答问题缓存 {question_hash: timestamp}
        
        # 违规警告功能
        self.enable_violation_warning = enable_violation_warning
        self.violation_warning_template = "@{user} 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。"
        
        # 初始化智谱AI分析器
        self.enable_ai_analysis = enable_ai_analysis
        self.zhipu_analyzer = None
        if enable_ai_analysis:
            self.zhipu_analyzer = ZhipuAnalyzer(api_key=zhipu_api_key)
            logger.info("智谱AI分析功能已启用")
        
        # 初始化数据存储
        self.enable_data_storage = enable_data_storage
        self.data_storage = None
        if enable_data_storage:
            from .data_storage import DataStorage
            self.data_storage = DataStorage(storage_path)
            logger.info("数据存储功能已启用")
        
        # 初始化问答处理器
        self.enable_qa = enable_qa
        self.qa_handler = None
        if enable_qa and my_username:
            self.qa_handler = QAHandler(
                my_username=my_username,
                zhipu_api_key=zhipu_api_key,
                enable_auto_reply=True,
                candidate_group_names=self.candidate_group_names
            )
            self.qa_handler.set_wechat_client(self.wx)
            logger.info(f"问答功能已启用，监听@{my_username}的消息")
        
        # 违规警告功能状态
        if self.enable_violation_warning:
            logger.info("违规自动警告功能已启用")
        
    def add_message_handler(self, handler: Callable[[Message], None]):
        """添加消息处理器"""
        self.message_handlers.append(handler)
        
    def start_monitoring(self, group_name: str, check_interval: int = 3) -> bool:
        """
        启动群聊监控 - 支持智能群聊名称查找
        
        Args:
            group_name: 群聊名称（可以是候选名称之一）
            check_interval: 检查间隔（秒）
            
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info(f"开始监控群聊: {group_name}")
            
            # 智能查找真实的群聊名称
            actual_group_name = self._find_actual_group_name(group_name)
            if not actual_group_name:
                logger.error(f"无法找到匹配的群聊名称，候选名称: {self.candidate_group_names}")
                return False
            
            logger.info(f"找到实际群聊名称: {actual_group_name}")
            
            # 获取会话列表并打开群聊
            self.wx.GetSessionList()
            result = self.wx.ChatWith(actual_group_name)
            
            if not result:
                logger.error(f"无法打开群聊: {actual_group_name}")
                # 尝试其他候选名称
                for candidate in self.candidate_group_names:
                    if candidate != actual_group_name:
                        logger.info(f"尝试备选群聊名称: {candidate}")
                        result = self.wx.ChatWith(candidate)
                        if result:
                            actual_group_name = candidate
                            logger.info(f"成功使用备选群聊名称: {candidate}")
                            break
                
                if not result:
                    logger.error(f"所有候选群聊名称都无法打开")
                    return False
                
            self.current_group = actual_group_name
            self.is_monitoring = True
            
            # 开始监控循环
            self._monitoring_loop(check_interval)
            
            return True
            
        except Exception as e:
            logger.error(f"启动监控失败: {e}")
            return False
    
    def _find_actual_group_name(self, target_group: str) -> str:
        """
        查找真实的群聊名称 - 尝试所有候选群聊名称
        
        Args:
            target_group: 目标群聊名称
            
        Returns:
            str: 实际的群聊名称，如果找不到返回空字符串
        """
        try:
            logger.info(f"🔍 开始查找群聊，目标: {target_group}")
            logger.info(f"📋 候选群聊列表: {self.candidate_group_names}")
            
            # 获取会话列表
            session_list = self.wx.GetSessionList()
            if not session_list:
                logger.warning("无法获取会话列表")
                return ""
            
            logger.debug(f"获取到会话列表: {session_list}")
            
            # 遍历所有候选群聊名称进行查找
            for i, candidate in enumerate(self.candidate_group_names, 1):
                logger.info(f"🔍 尝试第{i}个候选群聊: {candidate}")
                
                # 策略1: 检查候选群聊是否直接在会话列表中
                for j, session in enumerate(session_list):
                    clean_session = self._clean_session_name(session)
                    if clean_session == candidate:
                        logger.info(f"✅ 在会话列表中找到群聊 (位置{j+1}): {candidate}")
                        return clean_session
                
                # 策略2: 使用搜索框搜索候选群聊名称
                logger.info(f"🔍 搜索候选群聊: {candidate}")
                try:
                    # 直接搜索候选群聊名称，不做任何修改
                    self.wx.Search(candidate)
                    time.sleep(2)  # 等待搜索结果
                    
                    # 重新获取会话列表，看是否找到了目标群聊
                    updated_session_list = self.wx.GetSessionList()
                    if updated_session_list:
                        logger.debug(f"搜索 {candidate} 后的会话列表: {updated_session_list}")
                        
                        # 检查搜索结果中是否有完全匹配的群聊
                        for session in updated_session_list:
                            clean_session = self._clean_session_name(session)
                            if clean_session == candidate:
                                logger.info(f"✅ 通过搜索找到群聊: {candidate}")
                                return clean_session
                    
                    logger.warning(f"❌ 未找到候选群聊: {candidate}")
                    
                except Exception as e:
                    logger.warning(f"搜索群聊 {candidate} 时出错: {e}")
                    continue
            
            # 如果所有候选群聊都没找到，返回空字符串
            logger.error(f"❌ 所有候选群聊都未找到: {self.candidate_group_names}")
            return ""
            
        except Exception as e:
            logger.error(f"查找群聊名称失败: {e}")
            return ""
    
    def _clean_session_name(self, session_name: str) -> str:
        """
        清理会话名称，移除消息计数等额外信息
        
        Args:
            session_name: 原始会话名称
            
        Returns:
            str: 清理后的会话名称
        """
        try:
            # 移除常见的消息计数模式
            import re
            
            # 移除 "数字条新消息" 模式
            cleaned = re.sub(r'\d+条新消息$', '', session_name).strip()
            
            # 移除 "(数字)" 模式
            cleaned = re.sub(r'\(\d+\)$', '', cleaned).strip()
            
            # 移除 "[数字]" 模式
            cleaned = re.sub(r'\[\d+\]$', '', cleaned).strip()
            
            # 不移除末尾的数字，因为它们可能是群聊名称的一部分
            # 例如：test_demo1, 群聊2 等
            
            # 如果清理后为空，返回原始名称
            if not cleaned:
                return session_name
                
            return cleaned
            
        except Exception as e:
            logger.debug(f"清理会话名称失败: {e}")
            return session_name
    
    def _normalize_group_name(self, group_name: str) -> str:
        """
        标准化群聊名称，用于模糊匹配
        
        Args:
            group_name: 群聊名称
            
        Returns:
            str: 标准化后的群聊名称
        """
        try:
            import re
            # 移除所有空格、标点符号，转为小写
            normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', group_name.lower())
            return normalized
        except Exception as e:
            logger.debug(f"标准化群聊名称失败: {e}")
            return group_name.lower()
    
    def _reactivate_chat_window(self):
        """重新激活聊天窗口 - 如果找不到群就在候选群列表中换一个群搜索"""
        try:
            logger.info("🔧 尝试重新激活聊天窗口...")
            
            # 先激活微信主窗口
            self._activate_wechat_main_window()
            
            # 重新获取会话列表
            logger.debug("重新获取会话列表...")
            session_list = self.wx.GetSessionList()
            logger.debug(f"获取到会话列表: {session_list}")
            
            # 策略1: 如果有当前群聊，尝试重新打开
            if self.current_group:
                logger.debug(f"尝试重新打开当前群聊: {self.current_group}")
                result = self.wx.ChatWith(self.current_group)
                if result:
                    logger.info("✅ 当前群聊重新激活成功")
                    return True
                else:
                    logger.warning(f"❌ 当前群聊重新激活失败: {self.current_group}")
            
            # 策略2: 在候选群聊名称中逐个尝试
            logger.info("🔍 在候选群聊列表中查找可用群聊...")
            for i, candidate in enumerate(self.candidate_group_names, 1):
                logger.info(f"🔍 尝试第{i}个候选群聊: {candidate}")
                
                # 首先检查是否在会话列表中
                found_in_session = False
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    if clean_session == candidate:
                        logger.info(f"✅ 在会话列表中找到候选群聊: {candidate}")
                        result = self.wx.ChatWith(candidate)
                        if result:
                            logger.info(f"✅ 成功激活候选群聊: {candidate}")
                            self.current_group = candidate
                            logger.info("🔧 聊天窗口重新激活完成")
                            return True
                        found_in_session = True
                        break
                
                # 如果不在会话列表中，尝试搜索
                if not found_in_session:
                    logger.info(f"🔍 搜索候选群聊: {candidate}")
                    try:
                        self.wx.Search(candidate)
                        time.sleep(2)  # 等待搜索结果
                        
                        # 重新获取会话列表
                        updated_session_list = self.wx.GetSessionList()
                        if updated_session_list:
                            for session in updated_session_list:
                                clean_session = self._clean_session_name(session)
                                if clean_session == candidate:
                                    logger.info(f"✅ 通过搜索找到候选群聊: {candidate}")
                                    result = self.wx.ChatWith(candidate)
                                    if result:
                                        logger.info(f"✅ 成功激活搜索到的群聊: {candidate}")
                                        self.current_group = candidate
                                        logger.info("🔧 聊天窗口重新激活完成")
                                        return True
                                    break
                    except Exception as e:
                        logger.warning(f"搜索候选群聊 {candidate} 时出错: {e}")
                
                logger.warning(f"❌ 候选群聊 {candidate} 无法激活")
                time.sleep(0.5)  # 短暂等待后尝试下一个
            
            # 策略3: 如果所有候选群聊都无法激活，尝试使用会话列表中具有群聊特征的会话
            logger.info("🔍 尝试使用会话列表中具有群聊特征的会话...")
            if session_list:
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    # 检查是否看起来像群聊（包含群聊特征）
                    if self._looks_like_group_chat(clean_session):
                        logger.info(f"🔍 尝试使用具有群聊特征的会话: {clean_session}")
                        result = self.wx.ChatWith(clean_session)
                        if result:
                            logger.info(f"✅ 成功激活具有群聊特征的会话: {clean_session}")
                            self.current_group = clean_session
                            # 将这个群聊添加到候选列表开头
                            if clean_session not in self.candidate_group_names:
                                self.candidate_group_names.insert(0, clean_session)
                                logger.info(f"✅ 新群聊已添加到候选列表: {clean_session}")
                            logger.info("🔧 聊天窗口重新激活完成")
                            return True
                        time.sleep(0.5)
            
            logger.error("❌ 所有重新激活尝试都失败了")
            logger.error(f"💡 候选群聊列表: {self.candidate_group_names}")
            logger.error(f"💡 当前会话列表: {session_list}")
            return False
            
        except Exception as e:
            logger.error(f"重新激活聊天窗口出错: {e}")
            return False
    
    def _activate_wechat_main_window(self):
        """激活微信主窗口"""
        try:
            import uiautomation as auto
            
            # 查找微信主窗口
            wechat_window = auto.WindowControl(searchDepth=1, Name="微信")
            if wechat_window.Exists():
                logger.debug("找到微信主窗口: 微信")
                wechat_window.SetActive()
                time.sleep(0.5)
                logger.debug("✅ 微信主窗口已激活")
            else:
                logger.debug("未找到微信主窗口")
                
        except Exception as e:
            logger.debug(f"激活微信主窗口失败: {e}")
    
    def _looks_like_group_chat(self, session_name: str) -> bool:
        """判断会话名称是否看起来像群聊"""
        try:
            # 群聊特征关键词
            group_keywords = ["群", "group", "团队", "交流", "讨论", "工作", "项目", "学习", "班级", "demo", "test"]
            
            # 检查是否包含群聊关键词
            session_lower = session_name.lower()
            for keyword in group_keywords:
                if keyword in session_lower:
                    return True
            
            # 检查是否包含数字和字母的组合（如test_demo1）
            if (any(char.isdigit() for char in session_name) and 
                any(char.isalpha() for char in session_name) and
                len(session_name) >= 5):
                return True
            
            # 检查是否包含特殊符号（群聊名称常有的符号）
            if any(char in "-_()（）[]【】" for char in session_name):
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"判断群聊特征失败: {e}")
            return False
    
    def _is_similar_group_name(self, candidate: str, session: str) -> bool:
        """
        检查两个群聊名称是否相似（处理 test_demo1 vs test_demo2 的情况）
        
        Args:
            candidate: 候选群聊名称
            session: 会话列表中的群聊名称
            
        Returns:
            bool: 是否相似
        """
        try:
            # 转换为小写进行比较
            candidate_lower = candidate.lower()
            session_lower = session.lower()
            
            # 如果长度差异太大，不认为相似
            if abs(len(candidate_lower) - len(session_lower)) > 3:
                return False
            
            # 计算相似度
            similarity = self._calculate_similarity(candidate_lower, session_lower)
            
            # 如果相似度超过80%，认为是相似的
            if similarity > 0.8:
                logger.debug(f"群聊名称相似度: {candidate} vs {session} = {similarity:.2f}")
                return True
            
            # 特殊处理：检查是否只是末尾数字不同（如 test_demo1 vs test_demo2）
            import re
            
            # 移除末尾的数字
            candidate_base = re.sub(r'\d+$', '', candidate_lower)
            session_base = re.sub(r'\d+$', '', session_lower)
            
            # 如果去掉数字后完全相同，认为是相似的
            if candidate_base == session_base and candidate_base:
                logger.debug(f"群聊名称基础部分相同: {candidate_base}")
                return True
            
            # 检查是否只是中间数字不同（如 test_demo1 vs test_demo2）
            candidate_pattern = re.sub(r'\d+', r'\\d+', candidate_lower)
            if re.match(candidate_pattern, session_lower):
                logger.debug(f"群聊名称模式匹配: {candidate_pattern} matches {session_lower}")
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"计算群聊名称相似度失败: {e}")
            return False
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度（使用编辑距离）
        
        Args:
            str1: 字符串1
            str2: 字符串2
            
        Returns:
            float: 相似度（0-1之间）
        """
        try:
            # 使用编辑距离算法计算相似度
            def levenshtein_distance(s1, s2):
                if len(s1) < len(s2):
                    return levenshtein_distance(s2, s1)
                
                if len(s2) == 0:
                    return len(s1)
                
                previous_row = list(range(len(s2) + 1))
                for i, c1 in enumerate(s1):
                    current_row = [i + 1]
                    for j, c2 in enumerate(s2):
                        insertions = previous_row[j + 1] + 1
                        deletions = current_row[j] + 1
                        substitutions = previous_row[j] + (c1 != c2)
                        current_row.append(min(insertions, deletions, substitutions))
                    previous_row = current_row
                
                return previous_row[-1]
            
            max_len = max(len(str1), len(str2))
            if max_len == 0:
                return 1.0
            
            distance = levenshtein_distance(str1, str2)
            similarity = 1 - (distance / max_len)
            
            return similarity
            
        except Exception as e:
            logger.debug(f"计算字符串相似度失败: {e}")
            return 0.0
            
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.current_group = None
        logger.info("监控已停止")
        
    def get_monitoring_status(self) -> dict:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "current_group": self.current_group,
            "violation_count": len(self.violation_records),
            "last_check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
    def _monitoring_loop(self, check_interval: int):
        """监控主循环"""
        logger.info(f"开始监控循环，检查间隔: {check_interval}秒")
        
        while self.is_monitoring:
            try:
                # 获取最新消息
                new_messages = self._get_new_messages()
                
                # 处理新消息
                for message in new_messages:
                    self._process_message(message)
                    
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号，正在停止监控...")
                self.stop_monitoring()
                break
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(check_interval)
                
    def _get_new_messages(self) -> List[Message]:
        """获取新消息 - 使用最后处理的消息ID来过滤"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # 获取所有消息
                all_messages = self.wx.GetAllMessage
                
                if not all_messages:
                    if attempt < max_retries - 1:
                        logger.warning(f"获取消息为空，尝试重新激活窗口 (尝试 {attempt + 1}/{max_retries})")
                        self._reactivate_chat_window()
                        time.sleep(2)
                        continue
                    else:
                        logger.warning("多次尝试后仍无法获取消息")
                        return []
                
                new_messages = []
                
                # 如果是第一次运行，记录最后一条消息的ID，不处理任何历史消息
                if self.last_processed_message_id is None:
                    if all_messages:
                        # 获取最后一条消息的ID作为起始点
                        try:
                            last_raw_msg = all_messages[-1]
                            last_message = Message.from_raw_data(last_raw_msg, self.current_group)
                            self.last_processed_message_id = last_message.id
                            logger.info(f"监控初始化完成，从消息ID {self.last_processed_message_id} 开始监控，跳过 {len(all_messages)} 条历史消息")
                        except Exception as e:
                            logger.warning(f"初始化失败: {e}")
                            self.last_processed_message_id = "initialized"
                    else:
                        self.last_processed_message_id = "initialized"
                    return []  # 第一次运行不返回任何消息
                
                # 找到上次处理的消息位置
                last_processed_index = -1
                for i, raw_msg in enumerate(all_messages):
                    try:
                        message = Message.from_raw_data(raw_msg, self.current_group)
                        if message.id == self.last_processed_message_id:
                            last_processed_index = i
                            break
                    except:
                        continue
                
                # 如果找到了上次处理的消息，只处理之后的消息
                if last_processed_index >= 0:
                    new_raw_messages = all_messages[last_processed_index + 1:]
                else:
                    # 如果没找到上次处理的消息（可能消息列表已更新），只处理最新的1条
                    # 避免重复处理大量历史消息
                    new_raw_messages = all_messages[-1:] if all_messages else []
                    logger.warning(f"未找到上次处理的消息ID {self.last_processed_message_id}，只处理最新1条消息")
                
                # 处理新消息
                for raw_msg in new_raw_messages:
                    try:
                        message = Message.from_raw_data(raw_msg, self.current_group)
                        
                        # 跳过已处理的消息（双重保险）
                        if message.id in self.processed_message_ids:
                            logger.debug(f"跳过已处理消息: [{message.sender}] {message.content[:30]}...")
                            continue
                        
                        # 跳过自己发送的消息（在这里也进行过滤）
                        if self.my_username and message.sender == self.my_username:
                            logger.debug(f"跳过自己的消息（获取阶段）: [{message.sender}] {message.content[:30]}...")
                            self.processed_message_ids.add(message.id)  # 添加到已处理集合
                            continue
                        
                        # 跳过包含自己回复内容的消息（额外保护）
                        if self.my_username and f"@" in message.content and "决策树" in message.content and len(message.content) > 50:
                            logger.debug(f"跳过疑似自己的回复消息: [{message.sender}] {message.content[:50]}...")
                            self.processed_message_ids.add(message.id)
                            continue
                        
                        new_messages.append(message)
                        logger.debug(f"新消息: [{message.sender}] {message.content[:30]}...")
                        
                    except Exception as e:
                        logger.warning(f"解析消息失败，跳过: {raw_msg} - {e}")
                        continue
                
                # 更新最后处理的消息ID
                if new_messages:
                    self.last_processed_message_id = new_messages[-1].id
                    logger.debug(f"处理了 {len(new_messages)} 条新消息")
                    
                return new_messages
            
            except Exception as e:
                logger.error(f"获取新消息失败: {e}")
                if attempt < max_retries - 1:
                    logger.warning(f"尝试重新激活窗口后重试 (尝试 {attempt + 1}/{max_retries})")
                    self._reactivate_chat_window()
                    time.sleep(2)
                    continue
                else:
                    return []
            
    def _process_message(self, message: Message):
        """处理单条消息"""
        try:
            # 检测群名修改消息并处理
            if message.message_type == MessageType.SYSTEM and "修改群名为" in message.content:
                logger.info(f"检测到群名修改消息: {message.content}")
                self._handle_group_name_change(message)
                return
            
            # 过滤系统消息和时间消息
            if message.message_type in [MessageType.SYSTEM, MessageType.TIME]:
                return
            
            # 过滤自己的消息，避免处理自己发送的消息
            if self.my_username and message.sender == self.my_username:
                logger.debug(f"跳过自己的消息: [{message.sender}] {message.content}")
                # 仍然需要添加到已处理消息集合，避免重复处理
                self.processed_message_ids.add(message.id)
                return
            
            # 消息查重，避免重复处理
            if message.id in self.processed_message_ids:
                logger.debug(f"跳过重复消息: [{message.sender}] {message.content}")
                return
            
            # 添加到已处理消息集合
            self.processed_message_ids.add(message.id)
            
            logger.info(f"处理消息: [{message.sender}] {message.content}")
            
            is_violation = False
            
            # 如果启用了问答功能，优先处理@消息
            if self.enable_qa and self.qa_handler:
                if self.qa_handler.handle_question(message):
                    # 问答查重机制
                    question_content = self.qa_handler.extract_question(message)
                    question_hash = hash(question_content.lower().strip())
                    current_time = datetime.now()
                    
                    # 检查消息时间戳，只处理最近的消息（避免处理历史消息）
                    try:
                        if isinstance(message.timestamp, (int, float)):
                            message_time = datetime.fromtimestamp(message.timestamp)
                        elif isinstance(message.timestamp, datetime):
                            message_time = message.timestamp
                        else:
                            # 如果时间戳格式不正确，跳过时间检查
                            message_time = current_time
                        time_diff = (current_time - message_time).total_seconds()
                    except Exception as e:
                        logger.debug(f"时间戳处理失败，跳过时间检查: {e}")
                        time_diff = 0  # 设为0，不跳过消息
                    
                    # 如果消息超过10分钟，认为是历史消息，跳过处理
                    if time_diff > 600:  # 10分钟
                        logger.info(f"跳过历史问答消息 (时间差: {time_diff:.1f}秒): [{message.sender}] {question_content}")
                        return
                    
                    # 检查是否是重复问题（5分钟内）
                    if question_hash in self.recent_qa_questions:
                        last_time = self.recent_qa_questions[question_hash]
                        if (current_time - last_time).total_seconds() < 300:  # 5分钟内
                            logger.info(f"跳过重复问答: [{message.sender}] {question_content}")
                            return
                    
                    # 记录问答时间
                    self.recent_qa_questions[question_hash] = current_time
                    logger.info(f"已处理问答消息: [{message.sender}] {message.content}")
            
            # 如果启用了AI分析，进行违规检测
            if self.enable_ai_analysis and self.zhipu_analyzer:
                is_violation = self._analyze_message_for_violations(message)
            
            # 如果启用了数据存储，保存消息
            if self.enable_data_storage and self.data_storage:
                self.data_storage.add_message(message, is_violation)
            
            # 调用所有消息处理器
            for handler in self.message_handlers:
                try:
                    handler(message)
                except Exception as e:
                    logger.error(f"消息处理器执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
    
    def _handle_group_name_change(self, message: Message):
        """
        处理群名修改的系统消息
        
        Args:
            message: 包含群名修改信息的系统消息
        """
        try:
            import re
            
            # 解析群名修改消息，提取新的群名
            # 消息格式通常是："凡梦"修改群名为"test_demo2"
            logger.info(f"🔄 开始处理群名修改事件: {message.content}")
            
            # 尝试多种可能的格式
            patterns = [
                r'修改群名为"([^"]+)"',  # 标准格式：修改群名为"群名"
                r'修改群名为"([^"]*)"',  # 允许空群名
                r'修改群名为(.+)$',      # 没有引号的格式
                r'群名为"([^"]+)"',      # 简化格式：群名为"群名"
                r'"([^"]*)"修改群名为"([^"]*)"',  # 完整格式："用户"修改群名为"群名"
            ]
            
            new_group_name = None
            for pattern in patterns:
                match = re.search(pattern, message.content)
                if match:
                    if pattern == r'"([^"]*)"修改群名为"([^"]*)"':
                        # 对于完整格式，取第二个匹配组（群名）
                        new_group_name = match.group(2).strip()
                    else:
                        # 对于其他格式，取第一个匹配组
                        new_group_name = match.group(1).strip()
                    
                    if new_group_name:
                        logger.info(f"✅ 成功解析新群名: '{new_group_name}' (使用模式: {pattern})")
                        break
                    else:
                        logger.debug(f"解析到空群名，尝试下一个模式")
            
            if not new_group_name:
                logger.warning(f"❌ 无法解析群名修改消息: {message.content}")
                return
            
            # 记录旧群名
            old_group_name = self.current_group
            logger.info(f"📝 群名变更: '{old_group_name}' -> '{new_group_name}'")
            
            # 更新候选群聊列表
            self._update_candidate_groups_after_name_change(old_group_name, new_group_name)
            
            # 尝试重新连接到新群聊
            success = self._reconnect_to_new_group(new_group_name)
            
            if success:
                logger.info(f"🎉 群名修改处理成功，已重新连接到: '{new_group_name}'")
                
                # 记录群名修改事件到数据存储
                if self.enable_data_storage and self.data_storage:
                    try:
                        self.data_storage.log_group_name_change(old_group_name, new_group_name, message.timestamp)
                    except Exception as e:
                        logger.warning(f"记录群名修改事件失败: {e}")
            else:
                logger.error(f"❌ 群名修改处理失败，无法重新连接到: '{new_group_name}'")
                
        except Exception as e:
            logger.error(f"处理群名修改失败: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
    
    def _update_candidate_groups_after_name_change(self, old_name: str, new_name: str):
        """
        群名修改后更新候选群聊列表
        
        Args:
            old_name: 旧群名
            new_name: 新群名
        """
        try:
            logger.info(f"🔄 更新候选群聊列表: '{old_name}' -> '{new_name}'")
            
            # 如果新群名不在候选列表中，添加到列表开头
            if new_name not in self.candidate_group_names:
                self.candidate_group_names.insert(0, new_name)
                logger.info(f"✅ 新群名已添加到候选列表开头: '{new_name}'")
            else:
                # 如果新群名已在列表中，将其移到开头
                self.candidate_group_names.remove(new_name)
                self.candidate_group_names.insert(0, new_name)
                logger.info(f"✅ 新群名已移动到候选列表开头: '{new_name}'")
            
            # 保留旧群名在列表中（以防回滚或其他情况）
            if old_name and old_name not in self.candidate_group_names:
                self.candidate_group_names.append(old_name)
                logger.info(f"📝 旧群名已保留在候选列表中: '{old_name}'")
            
            logger.info(f"📋 更新后的候选群聊列表: {self.candidate_group_names[:5]}...")  # 只显示前5个
            
        except Exception as e:
            logger.error(f"更新候选群聊列表失败: {e}")
    
    def _reconnect_to_new_group(self, new_group_name: str) -> bool:
        """
        重新连接到新群聊
        
        Args:
            new_group_name: 新群聊名称
            
        Returns:
            bool: 重新连接是否成功
        """
        try:
            logger.info(f"🔄 尝试重新连接到新群聊: '{new_group_name}'")
            
            # 等待一下，让微信界面更新
            time.sleep(2)
            
            # 方法1: 直接尝试打开新群聊
            logger.info(f"🔍 方法1: 直接尝试打开新群聊")
            result = self.wx.ChatWith(new_group_name)
            if result:
                logger.info(f"✅ 方法1成功: 直接打开新群聊 '{new_group_name}'")
                self.current_group = new_group_name
                return True
            
            # 方法2: 重新获取会话列表并查找
            logger.info(f"🔍 方法2: 重新获取会话列表并查找")
            session_list = self.wx.GetSessionList()
            if session_list:
                logger.debug(f"获取到会话列表: {session_list}")
                
                # 在会话列表中查找新群名
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    if clean_session == new_group_name:
                        logger.info(f"✅ 在会话列表中找到新群聊: '{new_group_name}'")
                        result = self.wx.ChatWith(new_group_name)
                        if result:
                            logger.info(f"✅ 方法2成功: 通过会话列表打开新群聊")
                            self.current_group = new_group_name
                            return True
            
            # 方法3: 使用搜索功能查找新群聊
            logger.info(f"🔍 方法3: 使用搜索功能查找新群聊")
            try:
                self.wx.Search(new_group_name)
                time.sleep(3)  # 等待搜索结果
                
                # 重新获取会话列表
                updated_session_list = self.wx.GetSessionList()
                if updated_session_list:
                    for session in updated_session_list:
                        clean_session = self._clean_session_name(session)
                        if clean_session == new_group_name:
                            logger.info(f"✅ 通过搜索找到新群聊: '{new_group_name}'")
                            result = self.wx.ChatWith(new_group_name)
                            if result:
                                logger.info(f"✅ 方法3成功: 通过搜索打开新群聊")
                                self.current_group = new_group_name
                                return True
            except Exception as e:
                logger.warning(f"搜索新群聊失败: {e}")
            
            # 方法4: 使用通用的重新激活窗口方法
            logger.info(f"🔍 方法4: 使用通用重新激活方法")
            if self._reactivate_chat_window():
                # 检查当前群聊是否已经是新群聊
                if self.current_group == new_group_name:
                    logger.info(f"✅ 方法4成功: 通用方法已连接到新群聊")
                    return True
            
            logger.error(f"❌ 所有重新连接方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"重新连接到新群聊失败: {e}")
            return False
            for pattern in patterns:
                match = re.search(pattern, message.content)
                if match:
                    if pattern == r'"([^"]*)"修改群名为"([^"]*)"':
                        # 对于完整格式，取第二个匹配组（群名）
                        new_group_name = match.group(2).strip()
                    else:
                        # 对于其他格式，取第一个匹配组
                        new_group_name = match.group(1).strip()
                    
                    if new_group_name:
                        logger.info(f"✅ 成功解析新群名: '{new_group_name}' (使用模式: {pattern})")
                        break
                    else:
                        logger.debug(f"解析到空群名，尝试下一个模式")
            
            if not new_group_name:
                logger.warning(f"❌ 无法解析群名修改消息: {message.content}")
                return
            
            # 记录旧群名
            old_group_name = self.current_group
            logger.info(f"📝 群名变更: '{old_group_name}' -> '{new_group_name}'")
            
            # 更新候选群聊列表
            self._update_candidate_groups_after_name_change(old_group_name, new_group_name)
            
            # 尝试重新连接到新群聊
            success = self._reconnect_to_new_group(new_group_name)
            
            if success:
                logger.info(f"🎉 群名修改处理成功，已重新连接到: '{new_group_name}'")
                
                # 记录群名修改事件到数据存储
                if self.enable_data_storage and self.data_storage:
                    try:
                        self.data_storage.log_group_name_change(old_group_name, new_group_name, message.timestamp)
                    except Exception as e:
                        logger.warning(f"记录群名修改事件失败: {e}")
            else:
                logger.error(f"❌ 群名修改处理失败，无法重新连接到: '{new_group_name}'")
                
        except Exception as e:
            logger.error(f"处理群名修改失败: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
    
    def _update_candidate_groups_after_name_change(self, old_name: str, new_name: str):
        """
        群名修改后更新候选群聊列表
        
        Args:
            old_name: 旧群名
            new_name: 新群名
        """
        try:
            logger.info(f"🔄 更新候选群聊列表: '{old_name}' -> '{new_name}'")
            
            # 如果新群名不在候选列表中，添加到列表开头
            if new_name not in self.candidate_group_names:
                self.candidate_group_names.insert(0, new_name)
                logger.info(f"✅ 新群名已添加到候选列表开头: '{new_name}'")
            else:
                # 如果新群名已在列表中，将其移到开头
                self.candidate_group_names.remove(new_name)
                self.candidate_group_names.insert(0, new_name)
                logger.info(f"✅ 新群名已移动到候选列表开头: '{new_name}'")
            
            # 保留旧群名在列表中（以防回滚或其他情况）
            if old_name and old_name not in self.candidate_group_names:
                self.candidate_group_names.append(old_name)
                logger.info(f"📝 旧群名已保留在候选列表中: '{old_name}'")
            
            logger.info(f"📋 更新后的候选群聊列表: {self.candidate_group_names[:5]}...")  # 只显示前5个
            
        except Exception as e:
            logger.error(f"更新候选群聊列表失败: {e}")
    
    def _reconnect_to_new_group(self, new_group_name: str) -> bool:
        """
        重新连接到新群聊
        
        Args:
            new_group_name: 新群聊名称
            
        Returns:
            bool: 重新连接是否成功
        """
        try:
            logger.info(f"🔄 尝试重新连接到新群聊: '{new_group_name}'")
            
            # 等待一下，让微信界面更新
            time.sleep(2)
            
            # 方法1: 直接尝试打开新群聊
            logger.info(f"🔍 方法1: 直接尝试打开新群聊")
            result = self.wx.ChatWith(new_group_name)
            if result:
                logger.info(f"✅ 方法1成功: 直接打开新群聊 '{new_group_name}'")
                self.current_group = new_group_name
                return True
            
            # 方法2: 重新获取会话列表并查找
            logger.info(f"🔍 方法2: 重新获取会话列表并查找")
            session_list = self.wx.GetSessionList()
            if session_list:
                logger.debug(f"获取到会话列表: {session_list}")
                
                # 在会话列表中查找新群名
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    if clean_session == new_group_name:
                        logger.info(f"✅ 在会话列表中找到新群聊: '{new_group_name}'")
                        result = self.wx.ChatWith(new_group_name)
                        if result:
                            logger.info(f"✅ 方法2成功: 通过会话列表打开新群聊")
                            self.current_group = new_group_name
                            return True
            
            # 方法3: 使用搜索功能查找新群聊
            logger.info(f"🔍 方法3: 使用搜索功能查找新群聊")
            try:
                self.wx.Search(new_group_name)
                time.sleep(3)  # 等待搜索结果
                
                # 重新获取会话列表
                updated_session_list = self.wx.GetSessionList()
                if updated_session_list:
                    for session in updated_session_list:
                        clean_session = self._clean_session_name(session)
                        if clean_session == new_group_name:
                            logger.info(f"✅ 通过搜索找到新群聊: '{new_group_name}'")
                            result = self.wx.ChatWith(new_group_name)
                            if result:
                                logger.info(f"✅ 方法3成功: 通过搜索打开新群聊")
                                self.current_group = new_group_name
                                return True
            except Exception as e:
                logger.warning(f"搜索新群聊失败: {e}")
            
            # 方法4: 使用通用的重新激活窗口方法
            logger.info(f"🔍 方法4: 使用通用重新激活方法")
            if self._reactivate_chat_window():
                # 检查当前群聊是否已经是新群聊
                if self.current_group == new_group_name:
                    logger.info(f"✅ 方法4成功: 通用方法已连接到新群聊")
                    return True
            
            logger.error(f"❌ 所有重新连接方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"重新连接到新群聊失败: {e}")
            return False
                    if pattern == r'"([^"]*)"修改群名为"([^"]*)"':
                        # 对于完整格式，取第二个匹配组（群名）
                        new_group_name = match.group(2).strip()
                    else:
                        new_group_name = match.group(1).strip()
                    
                    # 移除可能的引号和其他特殊字符
                    new_group_name = new_group_name.strip('"\'`""''')
                    
                    # 如果提取的群名不为空，跳出循环
                    if new_group_name:
                        break
            
            if new_group_name:
                old_group_name = self.current_group
                
                logger.info(f"🔄 检测到群名修改: {old_group_name} -> {new_group_name}")
                
                # 更新当前群聊名称
                self.current_group = new_group_name
                
                # 将新群名添加到候选名称列表（如果不存在）
                if new_group_name not in self.candidate_group_names:
                    self.candidate_group_names.insert(0, new_group_name)  # 插入到列表开头
                    logger.info(f"✅ 新群名已添加到候选列表: {new_group_name}")
                
                # 更新问答处理器的候选群聊名称
                if self.enable_qa and self.qa_handler:
                    self.qa_handler.candidate_group_names = self.candidate_group_names
                    self.qa_handler.current_group_name = new_group_name
                    logger.info(f"✅ 问答处理器群名已更新: {new_group_name}")
                
                # 尝试切换到新的群聊
                try:
                    result = self.wx.ChatWith(new_group_name)
                    if result:
                        logger.info(f"✅ 成功切换到新群聊: {new_group_name}")
                    else:
                        logger.warning(f"❌ 切换到新群聊失败: {new_group_name}")
                        # 如果切换失败，尝试重新激活窗口
                        self._reactivate_chat_window()
                except Exception as e:
                    logger.warning(f"切换到新群聊时出错: {e}")
                
                # 记录群名变更事件
                logger.info(f"📝 群名变更记录: {old_group_name} -> {new_group_name}")
                print(f"🔄 群聊名称已更新: {old_group_name} -> {new_group_name}")
                
            else:
                logger.warning(f"无法解析群名修改消息，尝试了多种格式: {message.content}")
                logger.debug(f"消息原文: '{message.content}'")
                
        except Exception as e:
            logger.error(f"处理群名修改失败: {e}")
            
    def _analyze_message_for_violations(self, message: Message):
        """分析消息是否违规"""
        try:
            analysis_result = None
            
            # 分析文本消息
            if message.message_type == MessageType.TEXT:
                analysis_result = self.zhipu_analyzer.analyze_text_content(message.content)
            
            # 分析图片消息（暂时跳过，因为需要图片路径）
            elif message.message_type == MessageType.IMAGE:
                logger.info(f"检测到图片消息，暂时跳过AI分析: [{message.sender}]")
                return False
            
            # 如果检测到违规，创建违规记录
            if analysis_result and analysis_result.is_violation:
                violation_record = ViolationRecord(
                    user_id=message.sender,  # 使用发送者作为用户ID
                    user_name=message.sender,
                    violation_type=analysis_result.violation_type,
                    violation_content=message.content,
                    detection_time=datetime.now(),
                    group_name=message.group_name,
                    confidence=analysis_result.confidence,
                    message_id=message.id
                )
                
                self.add_violation_record(violation_record)
                return True
                
            return False
                
        except Exception as e:
            logger.error(f"违规分析失败: {e}")
            return False
    
    def add_violation_record(self, record: ViolationRecord):
        """添加违规记录"""
        self.violation_records.append(record)
        logger.warning(f"检测到违规行为: [{record.user_name}] {record.violation_type}")
        
        # 如果启用了数据存储，也保存到存储中
        if self.enable_data_storage and self.data_storage:
            self.data_storage.add_violation_record(record)
        
        # 如果启用了违规警告，发送警告消息
        if self.enable_violation_warning:
            self._send_violation_warning(record)
        
    def get_violation_records(self) -> List[ViolationRecord]:
        """获取所有违规记录"""
        return self.violation_records.copy()
        
    def clear_violation_records(self):
        """清空违规记录"""
        self.violation_records.clear()
        logger.info("违规记录已清空")
    
    def save_daily_data(self, target_date: datetime = None) -> str:
        """
        保存指定日期的24小时数据到Excel文件
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            str: 保存的文件路径
        """
        if not self.enable_data_storage or not self.data_storage:
            logger.error("数据存储功能未启用")
            return ""
        
        if not self.current_group:
            logger.error("当前没有监控的群聊")
            return ""
        
        return self.data_storage.save_daily_messages(self.current_group, target_date)
    
    def save_realtime_data(self) -> str:
        """
        保存当前实时数据到Excel文件
        
        Returns:
            str: 保存的文件路径
        """
        if not self.enable_data_storage or not self.data_storage:
            logger.error("数据存储功能未启用")
            return ""
        
        if not self.current_group:
            logger.error("当前没有监控的群聊")
            return ""
        
        return self.data_storage.save_realtime_data(self.current_group)
    
    def get_data_stats(self) -> dict:
        """
        获取数据统计信息
        
        Returns:
            dict: 统计信息
        """
        if not self.enable_data_storage or not self.data_storage:
            return {"error": "数据存储功能未启用"}
        
        return self.data_storage.get_cache_stats()
    
    def clear_data_cache(self):
        """清空数据缓存"""
        if self.enable_data_storage and self.data_storage:
            self.data_storage.clear_cache()
            logger.info("数据缓存已清空")
    
    def get_qa_history(self) -> List[dict]:
        """
        获取问答历史记录
        
        Returns:
            List[dict]: 问答历史
        """
        if not self.enable_qa or not self.qa_handler:
            return []
        
        return self.qa_handler.get_qa_history()
    
    def clear_qa_history(self):
        """清空问答历史"""
        if self.enable_qa and self.qa_handler:
            self.qa_handler.clear_qa_history()
    
    def add_preset_qa(self, keyword: str, answer: str):
        """
        添加预设问答
        
        Args:
            keyword: 关键词
            answer: 回答
        """
        if self.enable_qa and self.qa_handler:
            self.qa_handler.add_preset_qa(keyword, answer)
    
    def set_qa_auto_reply(self, enabled: bool):
        """
        设置问答自动回复开关
        
        Args:
            enabled: 是否启用自动回复
        """
        if self.enable_qa and self.qa_handler:
            self.qa_handler.set_auto_reply(enabled)
    
    def set_qa_reply_delay(self, delay: int):
        """
        设置问答回复延迟
        
        Args:
            delay: 延迟秒数
        """
        if self.enable_qa and self.qa_handler:
            self.qa_handler.set_reply_delay(delay)
    
    def _send_violation_warning(self, record: ViolationRecord):
        """
        发送违规警告消息
        
        Args:
            record: 违规记录
        """
        try:
            # 生成警告消息
            warning_message = self.violation_warning_template.format(user=record.user_name)
            
            # 添加发送延迟，避免发送过快
            import time
            time.sleep(1)
            
            # 尝试发送警告消息到群聊
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 使用改进的消息发送方法
                    success = self._send_message_to_current_chat(warning_message)
                    
                    if success:
                        logger.info(f"已发送违规警告: {warning_message}")
                        return
                    else:
                        logger.warning(f"发送违规警告失败 (尝试 {attempt + 1}/{max_retries}): {warning_message}")
                        if attempt < max_retries - 1:
                            # 尝试重新激活群聊窗口
                            self._reactivate_chat_window()
                            time.sleep(2)  # 等待2秒后重试
                            
                except Exception as send_error:
                    logger.warning(f"发送警告时出错 (尝试 {attempt + 1}/{max_retries}): {send_error}")
                    if attempt < max_retries - 1:
                        # 尝试重新激活群聊窗口
                        self._reactivate_chat_window()
                        time.sleep(2)
            
            # 所有重试都失败了
            logger.error(f"发送违规警告最终失败，已重试{max_retries}次: {warning_message}")
            
            # 记录发送失败的警告到日志文件
            self._log_failed_warning(record, warning_message)
                
        except Exception as e:
            logger.error(f"发送违规警告出错: {e}")
            
    def _log_failed_warning(self, record: ViolationRecord, warning_message: str):
        """
        记录发送失败的警告消息
        
        Args:
            record: 违规记录
            warning_message: 警告消息
        """
        try:
            import os
            from datetime import datetime
            
            # 创建失败日志目录
            log_dir = os.path.join(self.data_storage.storage_path if self.data_storage else "./monitor_data", "failed_warnings")
            os.makedirs(log_dir, exist_ok=True)
            
            # 记录到失败日志文件
            log_file = os.path.join(log_dir, f"failed_warnings_{datetime.now().strftime('%Y%m%d')}.txt")
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 发送失败\n")
                f.write(f"用户: {record.user_name}\n")
                f.write(f"违规类型: {record.violation_type}\n")
                f.write(f"违规内容: {record.violation_content}\n")
                f.write(f"警告消息: {warning_message}\n")
                f.write("-" * 50 + "\n")
            
            logger.info(f"发送失败的警告已记录到: {log_file}")
            
        except Exception as e:
            logger.error(f"记录失败警告时出错: {e}")
    
    def set_violation_warning_template(self, template: str):
        """
        设置违规警告模板
        
        Args:
            template: 警告模板，使用{user}作为用户名占位符
        """
        self.violation_warning_template = template
        logger.info(f"违规警告模板已更新: {template}")
    
    def set_violation_warning_enabled(self, enabled: bool):
        """
        设置违规警告开关
        
        Args:
            enabled: 是否启用违规警告
        """
        self.enable_violation_warning = enabled
        status = "启用" if enabled else "禁用"
        logger.info(f"违规警告功能已{status}")
    
    def _send_message_to_current_chat(self, message: str) -> bool:
        """
        向当前聊天窗口发送消息 - 使用WeChat原生SendMsg避免窗口分离
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 优先使用WeChat类的SendMsg方法，避免窗口分离问题
            # 使用当前群聊名称
            chat_name = self.current_group if self.current_group else ""
            
            # 使用WeChat原生的SendMsg方法
            self.wx.SendMsg(message, chat_name, clear=True)
            logger.info(f"通过WeChat SendMsg发送消息成功: {message}")
            return True
            
        except Exception as e:
            logger.debug(f"WeChat SendMsg发送失败: {e}")
            # 备用方法：尝试使用空字符串作为群聊名称
            try:
                self.wx.SendMsg(message, "", clear=True)
                logger.debug(f"WeChat SendMsg发送消息成功 (空参数): {message}")
                return True
            except Exception as e2:
                logger.debug(f"WeChat SendMsg最终失败: {e2}")
                # 最后备用：使用PyOfficeRobot API（可能会分离窗口）
                try:
                    import PyOfficeRobot
                    PyOfficeRobot.chat.send_message(who=chat_name if chat_name else "test_demo1", message=message)
                    logger.info(f"通过PyOfficeRobot API发送消息成功（可能分离窗口）: {message}")
                    return True
                except Exception as e3:
                    logger.error(f"所有发送方法都失败: {e3}")
                    return False
    

    
    def _reactivate_chat_window(self):
        """
        重新激活聊天窗口 - 增强版本，包含多种修复策略
        """
        try:
            logger.info("🔧 尝试重新激活聊天窗口...")
            
            # 策略1: 激活微信主窗口
            self._activate_wechat_main_window()
            
            # 策略2: 重新获取会话列表并打开群聊
            try:
                logger.debug("重新获取会话列表...")
                session_list = self.wx.GetSessionList()
                logger.debug(f"获取到会话列表: {session_list}")
                
                if self.current_group:
                    logger.debug(f"尝试重新打开群聊: {self.current_group}")
                    result = self.wx.ChatWith(self.current_group)
                    if result:
                        logger.info(f"✅ 成功重新激活群聊: {self.current_group}")
                        time.sleep(2)  # 等待窗口稳定
                    else:
                        logger.warning(f"❌ 重新激活群聊失败: {self.current_group}")
                        
                        # 策略3: 尝试使用会话列表中的第一个群聊
                        if session_list and len(session_list) > 0:
                            first_session = session_list[0]
                            logger.info(f"尝试使用会话列表中的第一个群聊: {first_session}")
                            result = self.wx.ChatWith(first_session)
                            if result:
                                logger.info(f"✅ 成功激活会话列表中的群聊: {first_session}")
                                # 更新当前群聊名称
                                self.current_group = first_session
                            
            except Exception as e:
                logger.warning(f"重新获取会话列表失败: {e}")
            
            # 策略4: 检查并关闭可能的独立窗口
            self._close_independent_windows()
            
            logger.info("🔧 聊天窗口重新激活完成")
            
        except Exception as e:
            logger.error(f"重新激活聊天窗口失败: {e}")
    
    def _activate_wechat_main_window(self):
        """激活微信主窗口"""
        try:
            import win32gui
            import win32con
            
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    if "微信" in window_text or "WeChat" in window_text:
                        windows.append((hwnd, window_text))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            # 找到微信主窗口并激活
            for hwnd, window_text in windows:
                if "微信" in window_text and len(window_text) <= 10:  # 主窗口标题通常较短
                    logger.debug(f"找到微信主窗口: {window_text}")
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(1)
                    logger.debug("✅ 微信主窗口已激活")
                    return True
                    
            logger.warning("❌ 未找到微信主窗口")
            return False
            
        except Exception as e:
            logger.debug(f"激活微信主窗口失败: {e}")
            return False
    
    def _close_independent_windows(self):
        """关闭可能的独立聊天窗口"""
        try:
            import win32gui
            import win32con
            
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    if "微信" in window_text or "WeChat" in window_text:
                        windows.append((hwnd, window_text))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            # 关闭独立的聊天窗口（标题较长的窗口）
            for hwnd, window_text in windows:
                if ("微信" in window_text or "WeChat" in window_text) and len(window_text) > 10:
                    logger.debug(f"发现可能的独立窗口: {window_text}")
                    # 尝试关闭独立窗口
                    win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                    time.sleep(0.5)
                    logger.debug(f"已尝试关闭独立窗口: {window_text}")
                    
        except Exception as e:
            logger.debug(f"关闭独立窗口失败: {e}")