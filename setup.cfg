[metadata]
name = PyOfficeRobot
version = 0.1.26
description = pip install PyOfficeRobot
long_description = file: README.md
long_description_content_type = text/markdown
url = https://www.python-office.com/office/robot.html
author = CoderWanFeng
author_email = <EMAIL>
license = MIT
license_file = LICENSE
platforms = any

project_urls =
    Bug Tracker = https://github.com/CoderWanFeng/PyOfficeRobot/issues
    Documentation = https://github.com/CoderWanFeng/PyOfficeRobot/blob/main/README.md
    Source Code = https://github.com/CoderWanFeng/PyOfficeRobot

[options]
packages = find:
install_requires =
    uiautomation;platform_system=='Windows'
    pywin32;platform_system=='Windows'
    pywinauto;platform_system=='Windows'
    schedule
    pandas
    poai
    xlwt
    xlrd==1.2.0
    PySide6
    porobot
    loguru
    pofile

python_requires = >=3.6
include_package_data = True
zip_safe = False

