#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试引用内容截断功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

def test_quote_truncation():
    """测试引用内容截断功能"""
    
    # 引用内容清理函数（从group_monitor.py复制）
    def clean_quoted_content_truncate(content):
        """清理消息中的引用内容，从引用关键词开始截断所有后续内容"""
        try:
            original_content = content
            cleaned_content = original_content
            
            # 简化的清理逻辑：从"引用"关键词开始截断所有后续内容
            quote_keywords = [
                "引用的消息",
                "引用  的消息",  # 包含多个空格
                "引用 的消息",   # 包含一个空格
                "引用消息",
                "引用:",
                "引用：",
                "回复:",
                "回复：",
                "回复了",
            ]
            
            # 找到第一个引用关键词的位置，截断后续所有内容
            quote_position = -1
            found_keyword = None
            
            for keyword in quote_keywords:
                pos = cleaned_content.find(keyword)
                if pos != -1 and (quote_position == -1 or pos < quote_position):
                    quote_position = pos
                    found_keyword = keyword
            
            # 如果找到引用关键词，截断后续所有内容
            if quote_position != -1:
                cleaned_content = cleaned_content[:quote_position].strip()
                print(f"找到引用关键词 '{found_keyword}' 在位置 {quote_position}，截断后续内容")
            
            # 移除其他引用符号包围的内容
            import re
            quote_patterns = [
                r'「[^」]*」',  # 「引用内容」
                r'『[^』]*』',  # 『引用内容』
                r'>[^@\n]*\n', # > 引用内容
            ]
            
            for pattern in quote_patterns:
                cleaned_content = re.sub(pattern, '', cleaned_content)
            
            # 清理多余的空白字符
            cleaned_content = re.sub(r'\s+', ' ', cleaned_content).strip()
            
            print(f"清理引用内容: 原始[{len(original_content)}字符] -> 清理后[{len(cleaned_content)}字符]")
            print(f"原始内容: {original_content}")
            print(f"清理后: {cleaned_content}")
            
            return cleaned_content
            
        except Exception as e:
            print(f"清理引用内容失败: {e}")
            return content
    
    # 测试用例
    test_cases = [
        {
            "name": "用户的实际消息格式",
            "content": "@The end nlp，cv\n引用  的消息 : @The end I视界sjsj",
            "expected": "@The end nlp，cv"
        },
        {
            "name": "简单的引用格式",
            "content": "@The end transformer 你好\n引用的消息: 之前的内容",
            "expected": "@The end transformer 你好"
        },
        {
            "name": "引用在中间的消息",
            "content": "@The end 问题1 引用: 旧内容 @The end 问题2",
            "expected": "@The end 问题1"
        },
        {
            "name": "多个引用关键词",
            "content": "@The end 新问题 引用消息 旧内容 回复: 更多内容",
            "expected": "@The end 新问题"
        },
        {
            "name": "正常消息（无引用）",
            "content": "@The end transformer 请帮我解释一下这个代码",
            "expected": "@The end transformer 请帮我解释一下这个代码"
        },
        {
            "name": "只有引用内容",
            "content": "引用的消息: 这是引用的内容",
            "expected": ""
        },
    ]
    
    print("🧪 开始测试引用内容截断功能:")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        
        result = clean_quoted_content_truncate(test_case['content'])
        
        print(f"期望结果: {test_case['expected']}")
        
        # 检查结果是否符合期望
        if result.strip() == test_case['expected'].strip():
            print("✅ 测试通过")
            success_count += 1
        else:
            print("❌ 测试失败")
        
        print("-" * 40)
    
    print(f"\n🎉 测试完成！")
    print(f"📊 测试结果: {success_count}/{total_count} 通过")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎯 所有测试都通过了！引用内容截断功能工作正常。")
    else:
        print("⚠️ 有测试失败，需要进一步优化截断逻辑。")

if __name__ == "__main__":
    test_quote_truncation()
