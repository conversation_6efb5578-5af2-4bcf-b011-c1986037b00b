# -*- coding: UTF-8 -*-
"""
问答处理模块
当有人@用户时，自动读取消息并回答问题
"""

import re
import time
from typing import Optional, List, Callable
from datetime import datetime
from loguru import logger

from ..WeChatType import WeChat
from .models import Message, MessageType
from .zhipu_analyzer import ZhipuAnalyzer


class QAHandler:
    """问答处理器"""
    
    def __init__(self, my_username: str, zhipu_api_key: str = None, 
                 enable_auto_reply: bool = True, reply_delay: int = 2,
                 candidate_group_names: list = None):
        """
        初始化问答处理器
        
        Args:
            my_username: 我的用户名（用于检测@我的消息）
            zhipu_api_key: 智谱AI API密钥
            enable_auto_reply: 是否启用自动回复
            reply_delay: 回复延迟（秒）
            candidate_group_names: 候选群聊名称列表
        """
        self.my_username = my_username
        self.enable_auto_reply = enable_auto_reply
        self.reply_delay = reply_delay
        self.wx = None  # 将在需要时设置
        
        # 候选群聊名称列表
        self.candidate_group_names = candidate_group_names or [
            "test_demo1",
            "浪前 6.1",
            "浪前6.1-运营官",
            "浪前6.1-运营官交流群",
            "浪前 6.1-运营官交流群（全员）",
            "测试群",
            "工作群",
            "学习群"
        ]
        
        # 当前使用的群聊名称
        self.current_group_name = None
        
        # 初始化AI分析器用于问答
        self.zhipu_analyzer = None
        if zhipu_api_key:
            self.zhipu_analyzer = ZhipuAnalyzer(api_key=zhipu_api_key)
            logger.info("问答AI功能已启用")
        else:
            self.zhipu_analyzer = ZhipuAnalyzer(api_key="5fe933a8186a410eba8171e71248e6d9.ZZbSscsop8f2Riky")
            logger.info("问答AI功能已启用")
        
        # 预设问答库（针对学习群优化）
        self.preset_qa = {
            # 基础问候
            "你好": "你好！我是群聊学习助手，有什么学习问题可以帮助你的吗？",
            "hi": "Hi！有什么技术问题需要帮助吗？",
            "hello": "Hello！欢迎提问，我会尽力帮助你解决问题。",
            
            # 时间相关
            "时间": f"现在时间是：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "几点": f"现在是：{datetime.now().strftime('%H:%M')}",
            
            # 帮助和功能
            "帮助": "我是学习助手，可以回答编程、开发、工具使用等问题。直接@我提问即可！",
            "功能": "我支持：技术问答、学习指导、代码解释、工具推荐、问题诊断等。有问题尽管问！",
            
            # 学习相关
            "学习": "学习编程需要多练习、多思考。有具体的学习问题可以详细问我哦！",
            "入门": "编程入门建议：选择一门语言深入学习，多写代码，多看文档，遇到问题及时提问。",
            "资料": "推荐学习资源：官方文档、GitHub项目、技术博客、在线教程。有特定需求可以详细问我。",
            
            # 技术相关
            "python": "Python是很棒的编程语言！有什么Python相关问题可以详细问我。",
            "代码": "代码问题我很乐意帮忙！请贴出具体代码和错误信息，我来帮你分析。",
            "bug": "遇到bug不要慌！请描述具体现象和错误信息，我来帮你排查问题。",
            "报错": "报错信息很重要！请把完整的错误信息发出来，我帮你分析原因和解决方案。",
            
            # 感谢和礼貌用语
            "谢谢": "不客气！能帮到你我很开心，有其他问题随时问我。",
            "感谢": "不用谢！学习路上互相帮助，有问题继续找我哦。",
            "辛苦": "不辛苦！帮助大家学习是我的职责，有问题尽管来问。",
        }
        
        # 问答历史记录
        self.qa_history = []
        
    def set_wechat_client(self, wx_client: WeChat):
        """设置微信客户端"""
        self.wx = wx_client
    
    def is_mentioned(self, message: Message) -> bool:
        """
        检查消息是否@了我
        
        Args:
            message: 消息对象
            
        Returns:
            bool: 是否@了我
        """
        if message.message_type != MessageType.TEXT:
            return False
        
        # 检查是否包含@我的用户名
        at_patterns = [
            f"@{self.my_username}",
            f"@ {self.my_username}",
            f"@{self.my_username} ",
        ]
        
        for pattern in at_patterns:
            if pattern in message.content:
                return True
        
        return False
    
    def extract_question(self, message: Message) -> str:
        """
        从@消息中提取问题内容
        
        Args:
            message: 消息对象
            
        Returns:
            str: 提取的问题
        """
        content = message.content
        
        # 移除@用户名部分
        at_patterns = [
            f"@{self.my_username}",
            f"@ {self.my_username}",
        ]
        
        for pattern in at_patterns:
            content = content.replace(pattern, "").strip()
        
        return content
    
    def generate_answer(self, question: str, sender: str) -> str:
        """
        生成问题的答案
        
        Args:
            question: 问题内容
            sender: 提问者
            
        Returns:
            str: 答案
        """
        try:
            # 首先检查预设问答
            for keyword, answer in self.preset_qa.items():
                if keyword in question:
                    return f"@{sender} {answer}"
            
            # 如果有AI分析器，使用AI生成答案
            if self.zhipu_analyzer and self.zhipu_analyzer.client:
                ai_answer = self._generate_ai_answer(question, sender)
                if ai_answer:
                    return ai_answer
            
            # 默认回复
            return f"@{sender} 抱歉，我暂时无法回答这个问题。你可以尝试问一些其他问题。"
            
        except Exception as e:
            logger.error(f"生成答案失败: {e}")
            return f"@{sender} 抱歉，处理问题时出现了错误。"
    
    def _generate_ai_answer(self, question: str, sender: str) -> Optional[str]:
        """
        使用AI生成答案（GLM-4.5-Flash模型）
        
        Args:
            question: 问题内容
            sender: 提问者
            
        Returns:
            Optional[str]: AI生成的答案
        """
        try:
            # 群聊助手系统提示词
            system_prompt = """你是一个专业的群聊学习助手，主要职责是帮助学员解答各种学习和技术问题。

你的特点：
- 专业：具备丰富的技术知识，能够准确回答编程、开发、工具使用等问题
- 耐心：对学员的问题保持耐心，即使是基础问题也会认真回答
- 友好：语气温和友善，让学员感到舒适和受欢迎
- 简洁：回答简洁明了，重点突出，避免冗长的解释
- 实用：提供具体可行的建议和解决方案

回答规范：
1. 回答长度控制在150字以内
2. 语气要友好专业，适合群聊环境
3. 如果问题不清楚，礼貌地要求澄清
4. 如果不知道答案，诚实说明并建议其他途径
5. 对于技术问题，尽量提供具体的解决步骤
6. 回答要以@提问者开头

你现在在一个学习群中，准备回答学员的问题。"""

            user_prompt = f"""学员 {sender} 问了一个问题：

{question}

请作为群聊学习助手回答这个问题。"""

            response = self.zhipu_analyzer.client.chat.completions.create(
                model="glm-4.5-flash",  # 使用GLM-4.5-Flash模型
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=300,
                top_p=0.9
            )
            
            answer = response.choices[0].message.content.strip()
            
            # 确保回答以@开头
            if not answer.startswith(f"@{sender}"):
                answer = f"@{sender} {answer}"
            
            return answer
            
        except Exception as e:
            logger.error(f"AI生成答案失败: {e}")
            return None
    
    def _contains_quoted_message(self, message: Message) -> bool:
        """
        检测消息是否包含引用的消息

        Args:
            message: 消息对象

        Returns:
            bool: 是否包含引用的消息
        """
        try:
            content = message.content.lower()

            # 检测引用消息的关键词（更精确的检测）
            quote_keywords = [
                "引用的消息",
                "引用  的消息",  # 特殊格式，包含多个空格
                "引用 的消息",   # 包含一个空格
                "引用消息",
                "回复了",
                "回复:",
                "引用:",
                "引用了",
                "「",  # 微信引用消息常用的符号
                "」",
                "『",
                "』",
                ">>",  # 常见的引用符号
                ">>>",
                "回复 ",
                "引用 ",
                "re:",
                "reply:",
                "quoted:",
            ]

            # 检查是否包含引用关键词
            for keyword in quote_keywords:
                if keyword in content:
                    logger.info(f"检测到引用消息关键词: '{keyword}' 在消息中: {message.content[:50]}...")
                    return True

            # 检测微信引用消息的特殊格式模式
            import re

            # 模式1: "用户名: 消息内容" 格式（常见的引用格式）
            if re.search(r'^[^:]+:\s*.+', content):
                logger.info(f"检测到引用消息格式模式1: {message.content[:50]}...")
                return True

            # 模式2: 包含换行符且包含引用特征（更精确的检测）
            lines = message.content.split('\n')
            if len(lines) > 1:
                # 检查是否有"引用"相关的行
                for line in lines:
                    line_lower = line.lower().strip()
                    if any(keyword in line_lower for keyword in ["引用", "回复", "："]):
                        logger.info(f"检测到引用消息格式模式2: {message.content[:50]}...")
                        return True

            # 模式3: 消息开头包含特殊符号组合
            quote_patterns = [
                r'^「.*?」',  # 「引用内容」
                r'^『.*?』',  # 『引用内容』
                r'^>+\s*',   # > 或 >> 开头
                r'^\[.*?\]', # [引用内容]
            ]

            for pattern in quote_patterns:
                if re.search(pattern, message.content):
                    logger.info(f"检测到引用消息格式模式: {pattern} 在消息中: {message.content[:50]}...")
                    return True

            return False

        except Exception as e:
            logger.error(f"检测引用消息失败: {e}")
            return False

    def handle_question(self, message: Message) -> bool:
        """
        处理问答消息

        Args:
            message: 消息对象

        Returns:
            bool: 是否处理了问答
        """
        try:
            # 检查是否@了我
            if not self.is_mentioned(message):
                return False

            # 提取问题（引用内容已在group_monitor中清理）
            question = self.extract_question(message)
            if not question:
                return False

            logger.info(f"收到问题: [{message.sender}] {question}")

            # 生成答案
            answer = self.generate_answer(question, message.sender)

            # 记录问答历史
            qa_record = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "提问者": message.sender,
                "问题": question,
                "答案": answer,
                "群聊": message.group_name
            }
            self.qa_history.append(qa_record)

            # 如果启用自动回复，发送答案
            if self.enable_auto_reply and self.wx:
                self._send_reply(answer, message.group_name)
            else:
                logger.info(f"生成答案: {answer}")

            return True

        except Exception as e:
            logger.error(f"处理问答失败: {e}")
            return False
    
    def _send_reply(self, answer: str, group_name: str):
        """
        发送回复消息
        
        Args:
            answer: 回复内容
            group_name: 群聊名称
        """
        try:
            # 添加延迟，模拟人工回复
            if self.reply_delay > 0:
                time.sleep(self.reply_delay)
            
            # 直接使用PyOfficeRobot发送消息
            success = self._send_message_with_pyofficerobot(answer, group_name)
            
            if success:
                logger.info(f"已发送回复: {answer}")
            else:
                logger.error(f"发送回复失败: {answer}")
                
        except Exception as e:
            logger.error(f"发送回复出错: {e}")
    
    def _send_message_with_pyofficerobot(self, message: str, group_name: str) -> bool:
        """
        使用PyOfficeRobot发送消息（优化版本，减少重复发送）
        
        Args:
            message: 要发送的消息
            group_name: 群聊名称
            
        Returns:
            bool: 发送是否成功
        """
        # 减少重试次数，避免重复发送
        max_retries = 2
        
        # 消息预处理 - 限制长度和清理特殊字符
        processed_message = self._preprocess_message(message)
        
        # 获取实际的群聊名称（处理群聊名称映射问题）
        actual_group_name = self._get_actual_group_name(group_name)
        
        # 如果群聊名称是纯数字，很可能是错误的，直接返回失败
        if actual_group_name.isdigit() and len(actual_group_name) > 4:
            logger.error(f"❌ 检测到可疑的数字群聊名称: {actual_group_name}，跳过发送")
            logger.error(f"💡 请检查群聊名称配置是否正确")
            return False
        
        for attempt in range(max_retries):
            try:
                import PyOfficeRobot
                
                logger.info(f"🔄 尝试发送消息 (第{attempt + 1}次): 长度={len(processed_message)}, 目标={actual_group_name}")
                
                # 在发送前先确保群聊窗口是打开的
                if attempt == 0:  # 只在第一次尝试时修复窗口状态
                    self._ensure_chat_window_active(actual_group_name)
                
                # 在每次尝试前都重新激活群聊窗口
                if attempt > 0:  # 从第二次尝试开始才重新激活
                    self._fix_wechat_window_state(actual_group_name)
                
                # 在发送前再次确保群聊窗口正确激活
                if attempt > 0:  # 从第二次尝试开始才重新激活
                    self._activate_chat_window_for_sending(actual_group_name)
                
                # 尝试发送消息，使用实际的群聊名称
                result = PyOfficeRobot.chat.send_message(who=actual_group_name, message=processed_message)
                
                if result:
                    logger.info(f"✅ 消息发送成功: {processed_message[:50]}...")
                    return True
                else:
                    logger.warning(f"❌ PyOfficeRobot返回发送失败 (尝试 {attempt + 1}/{max_retries})")
                    
                    # 等待一段时间，然后检查消息是否实际发送成功
                    time.sleep(2)
                    
                    # 通过检查是否能在群聊中找到刚发送的消息来验证发送状态
                    if self._verify_message_actually_sent(processed_message):
                        logger.info(f"✅ 验证发现消息实际已发送成功: {processed_message[:50]}...")
                        return True
                    
                    # 如果不是最后一次尝试，尝试查找新的群聊名称
                    if attempt < max_retries - 1:
                        logger.info(f"🔍 尝试查找新的群聊名称，当前使用: {actual_group_name}")
                        logger.debug(f"候选群聊名称列表: {self.candidate_group_names}")
                        new_group_name = self._find_available_group_name()
                        if new_group_name and new_group_name != actual_group_name:
                            logger.info(f"🔄 切换到新的群聊名称: {actual_group_name} -> {new_group_name}")
                            actual_group_name = new_group_name
                            # 更新当前群聊名称
                            if hasattr(self, 'current_group_name'):
                                self.current_group_name = new_group_name
                        else:
                            logger.warning(f"❌ 未找到新的可用群聊名称，继续使用: {actual_group_name}")
                        
                        logger.info(f"🔄 消息确实未发送，准备重试...")
                        time.sleep(1)
                        
            except Exception as e:
                logger.error(f"PyOfficeRobot发送消息出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        
        # 所有重试都失败，记录详细信息
        logger.error(f"所有发送尝试都失败")
        logger.error(f"消息长度: {len(processed_message)}")
        logger.error(f"目标群聊: {actual_group_name}")
        logger.error(f"消息内容: {processed_message[:100]}...")
        
        # 记录失败消息到文件
        self._log_failed_message(processed_message)
        
        return False
    
    def _find_available_group_name(self) -> str:
        """
        从候选群聊名称列表中查找可用的群聊名称
        
        Returns:
            str: 找到的可用群聊名称，如果没找到返回空字符串
        """
        try:
            if not self.wx:
                return ""
            
            # 获取当前会话列表
            session_list = self.wx.GetSessionList()
            if not session_list:
                logger.warning("无法获取会话列表")
                return ""
            
            logger.debug(f"查找可用群聊，会话列表: {session_list}")
            
            # 遍历候选群聊名称，查找在会话列表中的群聊
            for candidate in self.candidate_group_names:
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    
                    # 完全匹配
                    if clean_session == candidate:
                        logger.info(f"✅ 找到可用群聊 (完全匹配): {candidate}")
                        return clean_session
                    
                    # 包含匹配
                    elif candidate in clean_session or clean_session in candidate:
                        logger.info(f"✅ 找到可用群聊 (包含匹配): {candidate} -> {clean_session}")
                        return clean_session
            
            # 如果没有找到候选名称匹配，查找具有群聊特征的会话
            for session in session_list:
                clean_session = self._clean_session_name(session)
                
                # 群聊特征检测
                group_keywords = ["群", "group", "团队", "交流", "讨论", "工作", "项目", "学习", "班级"]
                if (any(keyword in clean_session.lower() for keyword in group_keywords) or
                    (any(char.isdigit() for char in clean_session) and 
                     any(char.isalpha() for char in clean_session) and
                     len(clean_session) >= 5) or
                    any(char in "-_()（）[]【】" for char in clean_session)):
                    
                    logger.info(f"🔍 找到可能的群聊: {clean_session}")
                    return clean_session
            
            logger.warning("❌ 未找到可用的群聊名称")
            return ""
            
        except Exception as e:
            logger.error(f"查找可用群聊名称失败: {e}")
            return ""
    
    def _verify_message_actually_sent(self, message: str) -> bool:
        """
        验证消息是否实际发送成功（简化版本）
        
        Args:
            message: 发送的消息内容
            
        Returns:
            bool: 消息是否实际发送成功
        """
        try:
            logger.debug("🔍 验证消息是否实际发送成功...")
            
            # 等待一段时间让消息出现在群聊中
            time.sleep(1)
            
            # 使用WeChat客户端读取最新消息
            if self.wx:
                try:
                    # 获取最新的几条消息
                    all_messages = self.wx.GetAllMessage
                    if not all_messages:
                        logger.debug("无法获取群聊消息进行验证")
                        return False
                    
                    # 检查最近的3条消息中是否包含我们发送的消息
                    recent_messages = all_messages[-3:] if len(all_messages) >= 3 else all_messages
                    
                    for raw_msg in recent_messages:
                        try:
                            sender, content, msg_id = raw_msg
                            
                            # 检查是否是我们发送的消息
                            if sender == self.my_username:
                                # 简单的内容匹配检查
                                message_clean = message.replace('\n', ' ').replace('\r', ' ').strip()
                                content_clean = content.replace('\n', ' ').replace('\r', ' ').strip()
                                
                                # 检查关键内容是否匹配（包含检查）
                                if len(message_clean) > 20 and message_clean[:20] in content_clean:
                                    logger.debug(f"✅ 验证成功：找到匹配的消息")
                                    return True
                                elif len(content_clean) > 20 and content_clean[:20] in message_clean:
                                    logger.debug(f"✅ 验证成功：找到匹配的消息")
                                    return True
                                    
                        except Exception as e:
                            logger.debug(f"解析消息时出错: {e}")
                            continue
                    
                    logger.debug("❌ 验证失败：未找到匹配的消息")
                    return False
                    
                except Exception as e:
                    logger.debug(f"验证消息时出错: {e}")
                    return False
            else:
                logger.debug("WeChat客户端不可用，无法验证消息")
                return False
                
        except Exception as e:
            logger.debug(f"验证消息发送状态时出错: {e}")
            return False
    
    def _verify_message_sent(self, message: str, group_name: str) -> bool:
        """
        验证消息是否真的发送成功（通过读取群聊内容确认）
        
        Args:
            message: 发送的消息内容
            group_name: 群聊名称
            
        Returns:
            bool: 消息是否真的发送成功
        """
        try:
            logger.info("🔍 正在验证消息是否发送成功...")
            
            # 等待一段时间让消息出现在群聊中
            time.sleep(2)
            
            # 使用WeChat客户端读取最新消息
            if self.wx:
                try:
                    # 确保当前在正确的群聊中
                    self.wx.GetSessionList()
                    self.wx.ChatWith(group_name)
                    time.sleep(1)
                    
                    # 获取最新的几条消息
                    all_messages = self.wx.GetAllMessage
                    if not all_messages:
                        logger.warning("无法获取群聊消息进行验证")
                        return False
                    
                    # 检查最近的5条消息中是否包含我们发送的消息
                    recent_messages = all_messages[-5:] if len(all_messages) >= 5 else all_messages
                    
                    for raw_msg in recent_messages:
                        try:
                            sender, content, msg_id = raw_msg
                            
                            # 检查是否是我们发送的消息
                            if sender == self.my_username:
                                # 检查消息内容是否匹配（允许部分匹配）
                                message_clean = message.replace('\n', ' ').replace('\r', ' ').strip()
                                content_clean = content.replace('\n', ' ').replace('\r', ' ').strip()
                                
                                # 检查关键内容是否匹配
                                if self._messages_match(message_clean, content_clean):
                                    logger.info(f"✅ 验证成功：在群聊中找到了我们发送的消息")
                                    logger.debug(f"发送的消息: {message_clean[:50]}...")
                                    logger.debug(f"群聊中的消息: {content_clean[:50]}...")
                                    return True
                                    
                        except Exception as e:
                            logger.debug(f"解析消息时出错: {e}")
                            continue
                    
                    logger.warning("❌ 验证失败：在群聊中未找到我们发送的消息")
                    logger.debug(f"查找的消息: {message[:50]}...")
                    logger.debug(f"最近消息发送者: {[raw_msg[0] for raw_msg in recent_messages]}")
                    
                    return False
                    
                except Exception as e:
                    logger.error(f"验证消息时出错: {e}")
                    return False
            else:
                logger.warning("WeChat客户端不可用，无法验证消息")
                return False
                
        except Exception as e:
            logger.error(f"验证消息发送状态时出错: {e}")
            return False
    
    def _messages_match(self, sent_message: str, received_message: str) -> bool:
        """
        检查发送的消息和接收到的消息是否匹配
        
        Args:
            sent_message: 发送的消息
            received_message: 接收到的消息
            
        Returns:
            bool: 消息是否匹配
        """
        try:
            # 清理消息内容
            sent_clean = ' '.join(sent_message.split())
            received_clean = ' '.join(received_message.split())
            
            # 完全匹配
            if sent_clean == received_clean:
                return True
            
            # 检查核心内容是否匹配（去除@用户名部分）
            sent_core = sent_clean
            received_core = received_clean
            
            # 移除@用户名部分进行比较
            import re
            sent_core = re.sub(r'@\w+\s*', '', sent_core).strip()
            received_core = re.sub(r'@\w+\s*', '', received_core).strip()
            
            # 检查核心内容是否匹配
            if sent_core in received_core or received_core in sent_core:
                return True
            
            # 检查关键词匹配（至少包含3个相同的关键词）
            sent_words = set(sent_core.split())
            received_words = set(received_core.split())
            common_words = sent_words.intersection(received_words)
            
            if len(common_words) >= 3 and len(sent_words) > 0:
                match_ratio = len(common_words) / len(sent_words)
                if match_ratio >= 0.6:  # 60%的词匹配
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"消息匹配检查出错: {e}")
            return False
    
    def _preprocess_message(self, message: str) -> str:
        """
        预处理消息内容
        
        Args:
            message: 原始消息
            
        Returns:
            str: 处理后的消息
        """
        try:
            # 限制消息长度
            if len(message) > 200:
                # 截断过长的消息
                processed = message[:180] + "..."
                logger.info(f"消息过长，已截断: {len(message)} -> {len(processed)}")
            else:
                processed = message
            
            # 清理可能有问题的字符
            processed = processed.replace('\n', ' ').replace('\r', ' ')
            processed = ' '.join(processed.split())  # 清理多余空格
            
            return processed
            
        except Exception as e:
            logger.error(f"消息预处理失败: {e}")
            return message[:100] + "..." if len(message) > 100 else message
    
    def _fix_wechat_window_state(self, group_name: str):
        """
        修复微信窗口状态
        
        Args:
            group_name: 群聊名称
        """
        try:
            logger.info("🔧 正在修复微信窗口状态...")
            
            # 方法1: 尝试重新激活微信主窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text or "WeChat" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 找到微信主窗口并激活
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:  # 主窗口标题通常较短
                        logger.info(f"🔧 找到微信主窗口: {window_text}")
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(1)
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信主窗口失败: {e}")
            
            # 方法2: 使用WeChat类重新打开群聊
            if self.wx:
                try:
                    logger.info(f"🔧 重新打开群聊: {group_name}")
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.info(f"✅ 成功重新打开群聊: {group_name}")
                    else:
                        logger.warning(f"❌ 重新打开群聊失败: {group_name}")
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"重新打开群聊失败: {e}")
            
            # 方法3: 尝试关闭独立窗口
            try:
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                for hwnd, window_text in windows:
                    # 如果窗口标题包含群聊名称，可能是独立窗口
                    if group_name in window_text and len(window_text) > 10:
                        logger.info(f"🔧 发现可能的独立窗口: {window_text}")
                        # 尝试关闭独立窗口
                        win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
                        time.sleep(0.5)
                        
            except Exception as e:
                logger.debug(f"关闭独立窗口失败: {e}")
                
            logger.info("🔧 微信窗口状态修复完成")
            
        except Exception as e:
            logger.error(f"修复微信窗口状态时出错: {e}")
    
    def _ensure_chat_window_active(self, group_name: str):
        """
        确保群聊窗口处于活跃状态
        
        Args:
            group_name: 群聊名称
        """
        try:
            logger.debug(f"🔧 确保群聊窗口活跃: {group_name}")
            
            if self.wx:
                # 获取会话列表并切换到目标群聊
                try:
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    
                    # 尝试切换到目标群聊
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.debug(f"✅ 成功切换到群聊: {group_name}")
                    else:
                        logger.warning(f"❌ 切换群聊失败: {group_name}")
                    
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.debug(f"切换群聊时出错: {e}")
            
            # 尝试激活微信窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text or "WeChat" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 激活微信主窗口
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.5)
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信窗口失败: {e}")
                
        except Exception as e:
            logger.error(f"确保群聊窗口活跃时出错: {e}")

    def _get_actual_group_name(self, group_name: str) -> str:
        """
        获取实际的群聊名称，处理群聊名称映射问题
        
        Args:
            group_name: 原始群聊名称
            
        Returns:
            str: 实际的群聊名称
        """
        try:
            # 直接返回群聊名称，不进行特殊的数字检测处理
            return group_name
            
        except Exception as e:
            logger.error(f"获取实际群聊名称失败: {e}")
            return group_name
    
    def _clean_session_name(self, session_name: str) -> str:
        """
        清理会话名称，移除消息计数等额外信息
        
        Args:
            session_name: 原始会话名称
            
        Returns:
            str: 清理后的会话名称
        """
        try:
            # 移除常见的消息计数模式
            import re
            
            # 移除 "数字条新消息" 模式
            cleaned = re.sub(r'\d+条新消息$', '', session_name).strip()
            
            # 移除 "(数字)" 模式
            cleaned = re.sub(r'\(\d+\)$', '', cleaned).strip()
            
            # 移除 "[数字]" 模式
            cleaned = re.sub(r'\[\d+\]$', '', cleaned).strip()
            
            # 移除末尾的数字（如果前面有非数字字符）
            if len(cleaned) > 1 and not cleaned[:-1].isdigit():
                cleaned = re.sub(r'\d+$', '', cleaned).strip()
            
            # 如果清理后为空，返回原始名称
            if not cleaned:
                return session_name
                
            return cleaned
            
        except Exception as e:
            logger.debug(f"清理会话名称失败: {e}")
            return session_name

    def _activate_chat_window_for_sending(self, group_name: str):
        """
        为发送消息激活聊天窗口
        
        Args:
            group_name: 群聊名称
        """
        try:
            logger.debug(f"🔧 为发送消息激活聊天窗口: {group_name}")
            
            # 方法1: 使用WeChat客户端重新打开群聊
            if self.wx:
                try:
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.debug(f"✅ 成功激活群聊窗口: {group_name}")
                        time.sleep(1)  # 等待窗口完全激活
                        return
                    else:
                        logger.warning(f"❌ 激活群聊窗口失败: {group_name}")
                except Exception as e:
                    logger.debug(f"使用WeChat客户端激活窗口失败: {e}")
            
            # 方法2: 尝试激活微信主窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 激活微信主窗口
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(1)
                        logger.debug(f"✅ 激活微信主窗口: {window_text}")
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信主窗口失败: {e}")
                
        except Exception as e:
            logger.error(f"激活聊天窗口失败: {e}")
    
    def _log_failed_message(self, message: str):
        """
        记录发送失败的消息到文件
        
        Args:
            message: 失败的消息内容
        """
        try:
            import os
            from datetime import datetime
            
            # 创建失败消息目录
            failed_dir = "supervisor_data/failed_warnings"
            os.makedirs(failed_dir, exist_ok=True)
            
            # 生成文件名
            today = datetime.now().strftime("%Y%m%d")
            filename = f"failed_warnings_{today}.txt"
            filepath = os.path.join(failed_dir, filename)
            
            # 记录失败消息
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] 发送失败的消息:\n")
                f.write(f"{message}\n")
                f.write("-" * 50 + "\n")
            
            logger.debug(f"失败消息已记录到: {filepath}")
            
        except Exception as e:
            logger.error(f"记录失败消息时出错: {e}")
            # 方法1: 使用WeChat客户端重新打开群聊
            if self.wx:
                try:
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.debug(f"✅ 成功激活群聊窗口: {group_name}")
                        time.sleep(1)  # 等待窗口完全激活
                        return
                    else:
                        logger.warning(f"❌ 激活群聊窗口失败: {group_name}")
                except Exception as e:
                    logger.debug(f"使用WeChat客户端激活窗口失败: {e}")
            
            # 方法2: 尝试激活微信主窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 激活微信主窗口
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(1)
                        logger.debug(f"✅ 激活微信主窗口: {window_text}")
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信主窗口失败: {e}")
                
        except Exception as e:
            logger.error(f"激活聊天窗口失败: {e}")
            # 方法1: 使用WeChat客户端重新打开群聊
            if self.wx:
                try:
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.debug(f"✅ 成功激活群聊窗口: {group_name}")
                        time.sleep(1)  # 等待窗口完全激活
                        return
                    else:
                        logger.warning(f"❌ 激活群聊窗口失败: {group_name}")
                except Exception as e:
                    logger.debug(f"使用WeChat客户端激活窗口失败: {e}")
            
            # 方法2: 尝试激活微信主窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 激活微信主窗口
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(1)
                        logger.debug(f"✅ 激活微信主窗口: {window_text}")
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信主窗口失败: {e}")
                
        except Exception as e:
            logger.error(f"激活聊天窗口失败: {e}")
            # 方法1: 使用WeChat客户端重新打开群聊
            if self.wx:
                try:
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.debug(f"✅ 成功激活群聊窗口: {group_name}")
                        time.sleep(1)  # 等待窗口完全激活
                        return
                    else:
                        logger.warning(f"❌ 激活群聊窗口失败: {group_name}")
                except Exception as e:
                    logger.debug(f"使用WeChat客户端激活窗口失败: {e}")
            
            # 方法2: 尝试激活微信主窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 激活微信主窗口
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(1)
                        logger.debug(f"✅ 激活微信主窗口: {window_text}")
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信主窗口失败: {e}")
                
        except Exception as e:
            logger.error(f"激活聊天窗口失败: {e}")
            # 方法1: 使用WeChat客户端重新打开群聊
            if self.wx:
                try:
                    self.wx.GetSessionList()
                    time.sleep(0.5)
                    result = self.wx.ChatWith(group_name)
                    if result:
                        logger.debug(f"✅ 成功激活群聊窗口: {group_name}")
                        time.sleep(1)  # 等待窗口完全激活
                        return
                    else:
                        logger.warning(f"❌ 激活群聊窗口失败: {group_name}")
                except Exception as e:
                    logger.debug(f"使用WeChat客户端激活窗口失败: {e}")
            
            # 方法2: 尝试激活微信主窗口
            try:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if "微信" in window_text:
                            windows.append((hwnd, window_text))
                    return True
                
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                
                # 激活微信主窗口
                for hwnd, window_text in windows:
                    if "微信" in window_text and len(window_text) <= 10:
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(1)
                        logger.debug(f"✅ 激活微信主窗口: {window_text}")
                        break
                        
            except Exception as e:
                logger.debug(f"激活微信主窗口失败: {e}")
                
        except Exception as e:
            logger.error(f"激活聊天窗口失败: {e}")

    def _get_current_chat_name(self) -> str:
        """获取当前聊天窗口的名称"""
        try:
            # 方法1: 从窗口标题获取
            import win32gui
            hwnd = win32gui.GetForegroundWindow()
            window_title = win32gui.GetWindowText(hwnd)
            
            if window_title and ("微信" in window_title or "WeChat" in window_title):
                # 移除"微信"等前缀，获取纯群聊名称
                chat_name = window_title.replace("微信", "").replace("WeChat", "").strip()
                if chat_name and chat_name != "":
                    logger.debug(f"从窗口标题获取聊天名称: {chat_name}")
                    return chat_name
            
            # 方法2: 从会话列表获取当前选中的会话
            try:
                session_list = self.wx.GetSessionList()
                if session_list:
                    # 通常第一个是当前活动的会话
                    current_session = session_list[0]
                    logger.debug(f"从会话列表获取聊天名称: {current_session}")
                    return current_session
            except Exception as e:
                logger.debug(f"从会话列表获取聊天名称失败: {e}")
            
            return ""
            
        except Exception as e:
            logger.error(f"获取当前聊天名称失败: {e}")
            return ""

    def _send_message_to_current_chat(self, message: str) -> bool:
        """
        向当前聊天窗口发送消息 - 使用WeChat原生SendMsg避免窗口分离
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 优先使用WeChat类的SendMsg方法，避免窗口分离问题
            # 获取当前聊天名称
            chat_name = self._get_current_chat_name()
            
            # 使用WeChat原生的SendMsg方法
            self.wx.SendMsg(message, chat_name, clear=True)
            logger.info(f"通过WeChat SendMsg发送消息成功: {message}")
            return True
            
        except Exception as e:
            logger.debug(f"WeChat SendMsg发送失败: {e}")
            # 备用方法：尝试使用空字符串作为chat_name
            try:
                self.wx.SendMsg(message, "", clear=True)
                logger.debug(f"WeChat SendMsg发送消息成功 (空参数): {message}")
                return True
            except Exception as e2:
                logger.debug(f"WeChat SendMsg最终失败: {e2}")
                # 最后备用：使用PyOfficeRobot API（可能会分离窗口）
                try:
                    import PyOfficeRobot
                    PyOfficeRobot.chat.send_message(who=chat_name if chat_name else "test_demo1", message=message)
                    logger.info(f"通过PyOfficeRobot API发送消息成功（可能分离窗口）: {message}")
                    return True
                except Exception as e3:
                    logger.error(f"所有发送方法都失败: {e3}")
                    return False
    

    

    
    def _send_message_with_retry(self, message: str, max_retries: int = 3) -> bool:
        """
        带重试机制的消息发送
        
        Args:
            message: 要发送的消息
            max_retries: 最大重试次数
            
        Returns:
            bool: 发送是否成功
        """
        for attempt in range(max_retries):
            try:
                logger.debug(f"尝试发送消息 (第{attempt + 1}次): {message}")
                
                # 尝试发送消息
                if self._send_message_to_current_chat(message):
                    logger.info(f"消息发送成功 (第{attempt + 1}次尝试)")
                    return True
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    wait_time = 2 * (attempt + 1)  # 递增等待时间
                    logger.debug(f"发送失败，{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    
                    # 重新激活微信窗口
                    try:
                        self.wx.UiaAPI.SwitchToThisWindow()
                        time.sleep(0.5)
                    except:
                        pass
                        
            except Exception as e:
                logger.error(f"第{attempt + 1}次发送尝试出错: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        
        # 所有重试都失败，记录到失败日志
        self._log_failed_message(message)
        logger.error(f"消息发送失败，已记录到失败日志: {message}")
        return False
    
    def _log_failed_message(self, message: str):
        """
        记录发送失败的消息到日志文件
        
        Args:
            message: 失败的消息
        """
        try:
            import os
            from datetime import datetime
            
            # 创建失败日志目录
            log_dir = "supervisor_data/failed_warnings"
            os.makedirs(log_dir, exist_ok=True)
            
            # 生成日志文件名
            today = datetime.now().strftime("%Y%m%d")
            log_file = os.path.join(log_dir, f"failed_warnings_{today}.txt")
            
            # 写入失败记录
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] 发送失败: {message}\n"
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            logger.debug(f"失败消息已记录到: {log_file}")
            
        except Exception as e:
            logger.error(f"记录失败消息出错: {e}")
    
    def get_qa_history(self) -> List[dict]:
        """
        获取问答历史记录
        
        Returns:
            List[dict]: 问答历史
        """
        return self.qa_history.copy()
    
    def clear_qa_history(self):
        """清空问答历史"""
        self.qa_history.clear()
        logger.info("问答历史已清空")
    
    def add_preset_qa(self, keyword: str, answer: str):
        """
        添加预设问答
        
        Args:
            keyword: 关键词
            answer: 回答
        """
        self.preset_qa[keyword] = answer
        logger.info(f"添加预设问答: {keyword} -> {answer}")
    
    def remove_preset_qa(self, keyword: str):
        """
        删除预设问答
        
        Args:
            keyword: 关键词
        """
        if keyword in self.preset_qa:
            del self.preset_qa[keyword]
            logger.info(f"删除预设问答: {keyword}")
    
    def get_preset_qa(self) -> dict:
        """
        获取预设问答库
        
        Returns:
            dict: 预设问答
        """
        return self.preset_qa.copy()
    
    def set_auto_reply(self, enabled: bool):
        """
        设置自动回复开关
        
        Args:
            enabled: 是否启用自动回复
        """
        self.enable_auto_reply = enabled
        status = "启用" if enabled else "禁用"
        logger.info(f"自动回复已{status}")
    
    def set_reply_delay(self, delay: int):
        """
        设置回复延迟
        
        Args:
            delay: 延迟秒数
        """
        self.reply_delay = delay
        logger.info(f"回复延迟设置为: {delay}秒")