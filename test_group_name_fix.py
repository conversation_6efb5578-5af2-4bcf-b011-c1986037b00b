# -*- coding: UTF-8 -*-
"""
测试群聊名称映射修复功能
"""

import sys
import time
sys.path.insert(0, '.')

try:
    from PyOfficeRobot.core.monitor.qa_handler import QAHandler
    from PyOfficeRobot.core.monitor.models import Message, MessageType
    from datetime import datetime
    print("✅ 导入成功")
except Exception as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_group_name_mapping():
    """测试群聊名称映射功能"""
    print("🧪 测试群聊名称映射修复功能")
    print("=" * 50)
    
    # 创建QA处理器
    qa_handler = QAHandler(
        my_username="The end",
        zhipu_api_key="5fe933a8186a410eba8171e71248e6d9.ZZbSscsop8f2Riky",
        enable_auto_reply=False  # 禁用自动回复，只测试名称映射
    )
    
    # 测试数字群聊名称映射
    print("\n1. 测试数字群聊名称映射:")
    digital_group_name = "123456"
    actual_name = qa_handler._get_actual_group_name(digital_group_name)
    print(f"   原始名称: {digital_group_name}")
    print(f"   映射后名称: {actual_name}")
    
    # 测试正常群聊名称
    print("\n2. 测试正常群聊名称:")
    normal_group_name = "test_demo1"
    actual_name2 = qa_handler._get_actual_group_name(normal_group_name)
    print(f"   原始名称: {normal_group_name}")
    print(f"   映射后名称: {actual_name2}")
    
    # 测试消息预处理
    print("\n3. 测试消息预处理:")
    long_message = "@凡梦 " + "这是一个很长的消息内容，" * 20
    processed = qa_handler._preprocess_message(long_message)
    print(f"   原始长度: {len(long_message)}")
    print(f"   处理后长度: {len(processed)}")
    print(f"   处理后内容: {processed[:100]}...")
    
    # 测试问答功能（不发送消息）
    print("\n4. 测试问答功能:")
    test_message = Message(
        id="test_001",
        sender="凡梦",
        content="@The end transformer是什么",
        message_type=MessageType.TEXT,
        timestamp=datetime.now(),
        group_name="123456"  # 使用数字群聊名称测试
    )
    
    # 检查是否@了我
    is_mentioned = qa_handler.is_mentioned(test_message)
    print(f"   是否@了我: {is_mentioned}")
    
    if is_mentioned:
        # 提取问题
        question = qa_handler.extract_question(test_message)
        print(f"   提取的问题: {question}")
        
        # 生成答案
        answer = qa_handler.generate_answer(question, test_message.sender)
        print(f"   生成的答案: {answer[:100]}...")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_group_name_mapping()