# -*- coding: UTF-8 -*-
'''
高级群聊监控功能演示
展示消息类型识别、去重、状态管理等功能
'''

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

import PyOfficeRobot
from datetime import datetime

class MessageStats:
    """消息统计类"""
    def __init__(self):
        self.total_messages = 0
        self.message_types = {}
        self.user_messages = {}
        self.start_time = datetime.now()
    
    def add_message(self, message):
        """添加消息到统计"""
        self.total_messages += 1
        
        # 统计消息类型
        msg_type = message.message_type.value
        self.message_types[msg_type] = self.message_types.get(msg_type, 0) + 1
        
        # 统计用户消息数
        if message.message_type.value not in ['system', 'time']:
            self.user_messages[message.sender] = self.user_messages.get(message.sender, 0) + 1
    
    def print_stats(self):
        """打印统计信息"""
        print("\n📊 消息统计信息")
        print("-" * 30)
        print(f"总消息数: {self.total_messages}")
        print(f"运行时间: {datetime.now() - self.start_time}")
        
        print("\n消息类型分布:")
        for msg_type, count in self.message_types.items():
            print(f"  {msg_type}: {count}")
        
        if self.user_messages:
            print("\n用户活跃度 (前5名):")
            sorted_users = sorted(self.user_messages.items(), key=lambda x: x[1], reverse=True)
            for i, (user, count) in enumerate(sorted_users[:5]):
                print(f"  {i+1}. {user}: {count} 条消息")

def create_advanced_handler(stats):
    """创建高级消息处理器"""
    def handler(message):
        # 添加到统计
        stats.add_message(message)
        
        # 根据消息类型显示不同的信息
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if message.message_type.value == "text":
            print(f"[{timestamp}] 💬 {message.sender}: {message.content}")
        elif message.message_type.value == "image":
            print(f"[{timestamp}] 🖼️ {message.sender} 发送了图片")
        elif message.message_type.value == "file":
            print(f"[{timestamp}] 📎 {message.sender} 发送了文件: {message.content}")
        elif message.message_type.value == "system":
            print(f"[{timestamp}] 🔔 系统消息: {message.content}")
        elif message.message_type.value == "recall":
            print(f"[{timestamp}] ↩️ {message.sender} 撤回了消息")
        else:
            print(f"[{timestamp}] ❓ {message.sender}: {message.content} ({message.message_type.value})")
    
    return handler

def main():
    """主函数"""
    print("🚀 高级群聊监控功能演示")
    print("=" * 50)
    
    # 显示支持的消息类型
    msg_types = PyOfficeRobot.group.get_group_message_types()
    print(f"📋 支持的消息类型: {', '.join(msg_types.keys())}")
    
    # 获取可用的群聊会话
    print("\n📱 获取微信会话列表...")
    sessions = PyOfficeRobot.group.get_group_sessions()
    
    if not sessions:
        print("❌ 未找到微信会话，请确保微信已登录")
        return
    
    print(f"✅ 找到 {len(sessions)} 个会话")
    if len(sessions) > 0:
        print("前5个会话:")
        for i, session in enumerate(sessions[:5]):
            print(f"  {i+1}. {session}")
    
    # 让用户选择群聊
    print(f"\n请输入要监控的群聊名称:")
    group_name = input("群聊名称: ").strip()
    
    if not group_name:
        print("❌ 未输入群聊名称")
        return
    
    # 创建统计对象和监控器
    stats = MessageStats()
    monitor = PyOfficeRobot.group.create_group_monitor()
    
    # 添加高级消息处理器
    handler = create_advanced_handler(stats)
    monitor.add_message_handler(handler)
    
    print(f"\n🎯 开始监控群聊: {group_name}")
    print("💡 功能特性:")
    print("  ✅ 实时消息监听")
    print("  ✅ 消息类型识别")
    print("  ✅ 消息去重处理")
    print("  ✅ 用户活跃度统计")
    print("  ✅ 按 Ctrl+C 查看统计并退出")
    print("-" * 50)
    
    try:
        monitor.start_monitoring(group_name, check_interval=3)
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")
        stats.print_stats()
    except Exception as e:
        print(f"❌ 监控出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()