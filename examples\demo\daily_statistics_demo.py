# -*- coding: UTF-8 -*-
'''
24小时群聊数据统计演示
展示如何统计从前一天24点到第二天24点的所有发言数据
'''

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

import PyOfficeRobot
from datetime import datetime, timed<PERSON><PERSON>

def create_statistics_handler():
    """创建统计消息处理器"""
    message_count = 0
    
    def handler(message):
        nonlocal message_count
        message_count += 1
        
        # 显示消息信息
        timestamp = datetime.now().strftime("%H:%M:%S")
        violation_status = ""
        
        print(f"[{timestamp}] 📊 第{message_count}条消息")
        print(f"  发言人: {message.sender}")
        print(f"  发言内容: {message.content}")
        print(f"  消息类型: {message.message_type.value}")
        print(f"  是否违规: 待检测{violation_status}")
        print("-" * 40)
    
    return handler

def main():
    """主函数"""
    print("🚀 24小时群聊数据统计演示")
    print("=" * 60)
    
    # 获取可用的群聊会话
    print("📱 获取微信会话列表...")
    sessions = PyOfficeRobot.group.get_group_sessions()
    
    if not sessions:
        print("❌ 未找到微信会话，请确保微信已登录")
        return
    
    print(f"✅ 找到 {len(sessions)} 个会话")
    if len(sessions) > 0:
        print("前5个会话:")
        for i, session in enumerate(sessions[:5]):
            print(f"  {i+1}. {session}")
    
    # 让用户选择群聊
    print(f"\n请输入要监控的群聊名称:")
    group_name = input("群聊名称: ").strip()
    
    if not group_name:
        print("❌ 未输入群聊名称")
        return
    
    # 创建带数据存储的监控器
    print("\n🔧 创建监控器（启用数据存储功能）...")
    monitor = PyOfficeRobot.group.create_group_monitor(
        enable_data_storage=True,
        storage_path="./daily_statistics"
    )
    print("✅ 监控器创建成功")
    
    # 添加统计处理器
    handler = create_statistics_handler()
    monitor.add_message_handler(handler)
    print("✅ 统计处理器添加成功")
    
    print(f"\n🎯 开始监控群聊: {group_name}")
    print("📊 功能特性:")
    print("  ✅ 实时消息统计")
    print("  ✅ 自动数据存储")
    print("  ✅ 24小时周期统计")
    print("  ✅ Excel格式导出")
    print("\n💡 操作说明:")
    print("  - 程序会自动记录所有消息")
    print("  - 按 Ctrl+C 停止监控并查看统计")
    print("  - 按 's' + Enter 保存当前数据")
    print("  - 按 'd' + Enter 保存今日24小时数据")
    print("-" * 60)
    
    try:
        # 启动监控
        monitor.start_monitoring(group_name, check_interval=3)
        
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")
        
        # 显示统计信息
        print("\n📊 数据统计信息:")
        stats = monitor.get_data_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 询问是否保存数据
        print("\n💾 数据保存选项:")
        print("1. 保存实时数据（所有已记录的消息）")
        print("2. 保存今日24小时数据（00:00-24:00）")
        print("3. 保存昨日24小时数据")
        print("4. 不保存，直接退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            print("💾 正在保存实时数据...")
            filepath = monitor.save_realtime_data()
            if filepath:
                print(f"✅ 实时数据已保存到: {filepath}")
            else:
                print("❌ 保存失败")
                
        elif choice == "2":
            print("💾 正在保存今日24小时数据...")
            filepath = monitor.save_daily_data(datetime.now())
            if filepath:
                print(f"✅ 今日数据已保存到: {filepath}")
            else:
                print("❌ 保存失败")
                
        elif choice == "3":
            print("💾 正在保存昨日24小时数据...")
            yesterday = datetime.now() - timedelta(days=1)
            filepath = monitor.save_daily_data(yesterday)
            if filepath:
                print(f"✅ 昨日数据已保存到: {filepath}")
            else:
                print("❌ 保存失败")
                
        else:
            print("👋 程序退出，数据未保存")
        
    except Exception as e:
        print(f"❌ 监控出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()