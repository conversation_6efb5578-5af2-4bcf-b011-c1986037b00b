# PyOfficeRobot 环境配置指南

## 📋 系统要求

- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: Python 3.6+ (推荐 Python 3.8+)
- **微信版本**: 微信 3.9 ([下载链接](https://pan.quark.cn/s/f32e9b832284))
- **内存**: 至少 4GB RAM
- **存储**: 至少 1GB 可用空间

## 🚀 快速安装

### 方法1: 使用安装脚本（推荐）

```bash
# 1. 克隆或下载项目
git clone <项目地址>
cd PyOfficeRobot-main

# 2. 运行安装脚本
python install_requirements.py
```

### 方法2: 手动安装

```bash
# 1. 升级pip
python -m pip install --upgrade pip

# 2. 安装完整依赖
pip install -r requirements.txt

# 3. 或安装最小依赖
pip install -r requirements-minimal.txt
```

### 方法3: 使用conda环境

```bash
# 1. 创建新环境
conda create -n pyofficerobot python=3.8

# 2. 激活环境
conda activate pyofficerobot

# 3. 安装依赖
pip install -r requirements.txt
```

## 📦 依赖包说明

### 核心依赖
- `pandas`: 数据处理和Excel操作
- `loguru`: 高级日志记录
- `schedule`: 定时任务调度
- `requests`: HTTP请求处理
- `openpyxl`: Excel文件读写

### Windows特定依赖
- `pywin32`: Windows API访问
- `pywinauto`: Windows应用程序自动化
- `uiautomation`: UI自动化控制

### AI功能依赖
- `zhipuai`: 智谱AI SDK（问答功能）
- `poai`: AI聊天功能
- `porobot`: 机器人功能

### 可选依赖
- `PySide6`: GUI框架（如需图形界面）
- `pyscreenshot`: 屏幕截图功能

## 🔧 环境配置

### 1. Python环境配置

```bash
# 检查Python版本
python --version

# 检查pip版本
pip --version

# 如果pip版本过低，升级pip
python -m pip install --upgrade pip
```

### 2. 虚拟环境配置（推荐）

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 微信配置

1. 下载并安装微信3.9版本
2. 登录微信账号
3. 确保微信处于前台运行状态
4. 关闭微信的自动更新功能

## 🐛 常见问题解决

### 问题1: pip安装失败

```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或使用阿里云镜像
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 问题2: Windows特定包安装失败

```bash
# 安装Microsoft C++ Build Tools
# 下载地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/

# 或使用预编译包
pip install --only-binary=all pywin32 pywinauto
```

### 问题3: zhipuai包安装失败

```bash
# 手动安装zhipuai
pip install zhipuai>=2.0.0

# 如果仍然失败，可以跳过AI功能
# 系统会自动降级到关键词匹配模式
```

### 问题4: 权限问题

```bash
# Windows: 以管理员身份运行命令提示符
# 或使用用户安装
pip install --user -r requirements.txt
```

## ✅ 安装验证

运行以下脚本验证安装：

```python
# test_installation.py
import sys

def test_imports():
    packages = [
        'pandas', 'loguru', 'schedule', 'requests', 'openpyxl'
    ]
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")

if __name__ == "__main__":
    print("🔍 检查依赖包安装状态...")
    test_imports()
    print("检查完成！")
```

## 🚀 启动项目

```bash
# 启动完整监督系统
python complete_supervisor.py

# 运行简单测试
python simple_test.py
```

## 📞 技术支持

如果遇到安装问题，请：

1. 检查Python版本是否符合要求
2. 确保网络连接正常
3. 尝试使用不同的pip镜像源
4. 查看错误日志并搜索解决方案
5. 在项目Issues中提交问题

## 📝 更新日志

- v1.0: 初始版本
- v1.1: 添加引用消息过滤功能
- v1.2: 完善依赖管理和安装脚本
