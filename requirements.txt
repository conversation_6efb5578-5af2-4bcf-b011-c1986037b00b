# PyOfficeRobot 项目依赖文件
# 用于在新环境中安装所有必要的依赖包

# ============================================
# 核心框架依赖
# ============================================
python-office>=1.0.0

# ============================================
# 数据处理和存储
# ============================================
pandas==2.2.2
openpyxl>=3.1.0          # Excel文件读写支持（pandas需要）
xlrd==1.2.0              # 旧版Excel文件读取
xlwt==1.3.0              # Excel文件写入

# ============================================
# Windows系统集成（仅Windows平台需要）
# ============================================
pywin32==306; platform_system=="Windows"
pywinauto==0.6.8; platform_system=="Windows"
uiautomation==2.0.18; platform_system=="Windows"

# ============================================
# GUI框架
# ============================================
PySide6==6.7.0
PySide6_Addons==6.7.0
PySide6_Essentials==6.7.0

# ============================================
# AI和机器人功能
# ============================================
poai==0.0.11             # AI聊天功能
porobot==0.0.3           # 机器人功能
zhipuai>=2.0.0           # 智谱AI SDK（问答功能）

# ============================================
# 任务调度和日志
# ============================================
schedule==1.2.1          # 定时任务调度
loguru>=0.7.0            # 高级日志库

# ============================================
# 网络请求和工具
# ============================================
requests==2.32.2         # HTTP请求库
pyscreenshot==3.1        # 屏幕截图功能

# ============================================
# 系统工具
# ============================================
setuptools>=69.0.0       # Python包管理工具

# ============================================
# 可选依赖（根据需要安装）
# ============================================
# typing_extensions>=4.0.0  # 类型注解扩展（Python < 3.8需要）
# pathlib2>=2.3.0          # 路径处理（Python < 3.4需要）
