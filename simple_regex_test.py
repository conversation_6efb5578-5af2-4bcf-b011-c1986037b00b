#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

import re

# 测试消息
msg = '"凡梦"修改群名为"test_demo1"'
print(f"测试消息: {msg}")

# 测试正则表达式
pattern = r'"([^"]*)"修改群名为"([^"]*)"'
print(f"正则模式: {pattern}")

match = re.search(pattern, msg)
print(f"匹配结果: {match}")

if match:
    print(f"完整匹配: {match.group(0)}")
    print(f"组1 (用户): {match.group(1)}")
    print(f"组2 (群名): {match.group(2)}")
    
    # 提取群名
    group_name = match.group(2).strip()
    print(f"提取的群名: '{group_name}'")
    
    # 清理群名
    cleaned = group_name.strip('"\'`""''「」【】')
    print(f"清理后: '{cleaned}'")
else:
    print("匹配失败")

# 测试其他模式
patterns = [
    r'修改群名为"([^"]+)"',
    r'修改群名为"([^"]*)"',
    r'"([^"]*)"修改群名为"([^"]*)"',
    r'群名为"([^"]+)"',
]

print(f"\n测试所有模式:")
for i, p in enumerate(patterns, 1):
    match = re.search(p, msg)
    if match:
        if p == r'"([^"]*)"修改群名为"([^"]*)"':
            result = match.group(2)
        else:
            result = match.group(1)
        print(f"模式{i}: ✅ 匹配 -> '{result}'")
    else:
        print(f"模式{i}: ❌ 不匹配")