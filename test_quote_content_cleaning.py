#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试引用内容清理功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

def test_quote_content_cleaning():
    """测试引用内容清理功能"""
    
    # 模拟消息类
    class MockMessage:
        def __init__(self, content):
            self.content = content
            self.sender = "测试用户"
            self.message_type = MockMessageType()
            self.timestamp = datetime.now()
            self.group_name = "test_demo1"
            self.id = "test_001"
            self.raw_data = ("测试用户", content, "test_001")
    
    class MockMessageType:
        def __init__(self):
            self.value = "text"
    
    # 引用内容清理函数（从group_monitor.py复制）
    def clean_quoted_content(message):
        """清理消息中的引用内容，只保留新的内容"""
        try:
            original_content = message.content
            cleaned_content = original_content
            
            # 方法1: 移除"引用的消息"及其后面的内容直到换行
            import re
            
            # 移除引用块（从"引用"开始到下一个@或消息结束）
            patterns = [
                r'引用\s*的消息\s*[:：]\s*[^@]*',  # 引用的消息: 内容
                r'引用\s+的消息\s*[:：]\s*[^@]*',  # 引用  的消息: 内容
                r'引用\s*[:：]\s*[^@]*',          # 引用: 内容
                r'回复\s*[:：]\s*[^@]*',          # 回复: 内容
            ]
            
            for pattern in patterns:
                cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.IGNORECASE | re.DOTALL)
            
            # 方法2: 移除引用符号包围的内容
            quote_patterns = [
                r'「[^」]*」',  # 「引用内容」
                r'『[^』]*』',  # 『引用内容』
                r'>[^@\n]*\n', # > 引用内容
            ]
            
            for pattern in quote_patterns:
                cleaned_content = re.sub(pattern, '', cleaned_content)
            
            # 方法3: 如果有多行，尝试保留包含@的行
            lines = cleaned_content.split('\n')
            if len(lines) > 1:
                # 保留包含@的行和其后的内容
                at_lines = []
                found_at = False
                for line in lines:
                    if '@' in line and not found_at:
                        found_at = True
                    if found_at:
                        at_lines.append(line)
                
                if at_lines:
                    cleaned_content = '\n'.join(at_lines)
            
            # 清理多余的空白字符
            cleaned_content = re.sub(r'\n+', '\n', cleaned_content).strip()
            
            # 如果清理后内容为空或只有空白字符，返回原消息
            if not cleaned_content or cleaned_content.isspace():
                print(f"清理后内容为空，保留原消息: {original_content[:50]}...")
                return message
            
            # 创建新的消息对象
            message.content = cleaned_content
            
            print(f"清理引用内容: 原始[{len(original_content)}字符] -> 清理后[{len(cleaned_content)}字符]")
            print(f"原始内容: {original_content}")
            print(f"清理后: {cleaned_content}")
            
            return message
            
        except Exception as e:
            print(f"清理引用内容失败: {e}")
            return message
    
    # 测试用例
    test_cases = [
        {
            "name": "实际的引用消息格式",
            "content": "@The end 安安安安安安安啊啊啊啊\n引用  的消息 : @凡梦 你好！看起来你的问题可能没有发送完整...",
            "expected_contains": "@The end 安安安安安安安啊啊啊啊"
        },
        {
            "name": "简单的引用格式",
            "content": "@The end transformer 你好\n引用的消息: 之前的内容",
            "expected_contains": "@The end transformer 你好"
        },
        {
            "name": "包含引用符号的消息",
            "content": "@The end transformer 「引用内容」我想问个问题",
            "expected_contains": "@The end transformer 我想问个问题"
        },
        {
            "name": "多行引用消息",
            "content": "引用: 之前的消息\n@The end transformer 这是新问题",
            "expected_contains": "@The end transformer 这是新问题"
        },
        {
            "name": "正常消息（无引用）",
            "content": "@The end transformer 请帮我解释一下这个代码",
            "expected_contains": "@The end transformer 请帮我解释一下这个代码"
        },
    ]
    
    print("🧪 开始测试引用内容清理功能:")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"原始消息: {test_case['content']}")
        
        message = MockMessage(test_case['content'])
        cleaned_message = clean_quoted_content(message)
        
        print(f"清理结果: {cleaned_message.content}")
        
        # 检查是否包含期望的内容
        if test_case['expected_contains'] in cleaned_message.content:
            print("✅ 测试通过 - 包含期望内容")
            success_count += 1
        else:
            print(f"❌ 测试失败 - 未找到期望内容: {test_case['expected_contains']}")
        
        print("-" * 40)
    
    print(f"\n🎉 测试完成！")
    print(f"📊 测试结果: {success_count}/{total_count} 通过")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")

if __name__ == "__main__":
    test_quote_content_cleaning()
