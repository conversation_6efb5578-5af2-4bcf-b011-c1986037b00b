<p align="center" id='大礼包-banner'>
    <a target="_blank" href='http://python4office.cn/fuli/fuli-source-0726/'>
    <img src="https://banner-1300615378.cos.ap-guangzhou.myqcloud.com/%E6%A8%AA%E6%9D%A1/Python%E5%A4%A7%E7%A4%BC%E5%8C%85.jpg" width="100%"/>
    </a>   
</p>
<p align="center">
	👉 <a target="_blank" href="https://www.python-office.com/office/robot.html">项目官网</a> 👈
</p>
<p align="center">
	👉 <a target="_blank" href="http://www.python4office.cn/wechat-group/">本开源项目的交流群</a> 👈
</p>


<p align="center" name="'github">
    <a target="_blank" href='https://github.com/CoderWanFeng/PyOfficeRobot'>
    <img src="https://img.shields.io/github/stars/CoderWanFeng/PyOfficeRobot.svg?style=social" alt="github star"/>
    </a>
    	<a target="_blank" href='https://gitee.com/CoderWanFeng//PyOfficeRobot/'>
		<img src='https://gitee.com/CoderWanFeng//PyOfficeRobot/badge/star.svg?theme=dark' alt='gitee star'/>
	</a>
  	<a href="https://mp.weixin.qq.com/s/yaSmFKO3RrBpyanW3nvRAQ">
	<img src="https://img.shields.io/badge/QQ-163434413-orange"/>
  </a>
    	<a href="http://www.python4office.cn/wechat-group/">
	<img src="https://img.shields.io/badge/%E5%BE%AE%E4%BF%A1-%E4%BA%A4%E6%B5%81%E7%BE%A4-brightgreen"/>
  </a>

</p>

# 又一个微信聊天机器人横空出世了，人人可用！

之前给大家分享过一个微信机器人：[一个15分钟的视频，教你用Python创建自己的微信聊天机器人！](http://t.cn/A66p30bI)

但是之前这个机器人，需要基于网页版才能用(有网页版微信的同学，还可以去继续用)；然而很多朋友的微信，是不能登录网页版微信的。

> 有没有一种微信机器人，任何人的微信都可以用，不需要网页微信功能呢？


在经过技术检索和开发以后，支持所有微信使用的：**PyOfficeRobot**来啦~

## 1、安装PyOfficeRobot

1行命令，安装PyOfficeRobot这个库。[安装视频](https://www.bilibili.com/video/BV1S84y1m7xd/?p=2/)

```
pip install -i https://mirrors.aliyun.com/pypi/simple/ PyOfficeRobot -U
```

目前不支持最新版微信，支持的微信版本时3.9，下载链接：[https://pan.quark.cn/s/f32e9b832284](https://pan.quark.cn/s/f32e9b832284)

## 2、功能演示

- ⭐本机器人使用完全免费，全部功能的演示视频，扫码下图直达👇 +
  项目源码：[Github](https://gitee.com/CoderWanFeng/PyOfficeRobot/demo)、[gitee](https://gitee.com/CoderWanFeng/PyOfficeRobot)

<p align="center" id='10讲机器人-banner'>
    <a target="_blank" href='https://www.python-office.com/course-002/10-PyOfficeRobot/10-PyOfficeRobot.html'>
    <img src="https://website-python-1300615378.cos.ap-nanjing.myqcloud.com/course/10%E8%AE%B2%E6%9C%BA%E5%99%A8%E4%BA%BA-%E6%A8%AA.jpg" width="100%"/>
    </a>   
</p>

> 持续更新中，交流群：[点我加入](http://www.python4office.cn/wechat-group/)

#### 微信机器人-其它实现方式

| 功能说明            | 视频                                                   | 代码                                                        |
|-----------------|------------------------------------------------------|-----------------------------------------------------------|
| 机器人.exe         | [点我直达](https://www.bilibili.com/video/BV1Q64y1Z7TB/) |                                                           |
| ChatGPT版本       | [点我直达](https://www.bilibili.com/video/BV1Dx4y157qy)  | [点我直达](https://mp.weixin.qq.com/s/HJfLZILUOWn4TK8qk3DL9w) |
| ⌚wxpy-24小时，后台运行 | [点我直达](https://www.bilibili.com/video/BV11L411L7oi/) | [点我直达](https://mp.weixin.qq.com/s/ubJ1OhOFVKfFVT8sBNZ0pg) |
| 企业微信机器人         |                                                      | [点我直达](https://mp.weixin.qq.com/s/mt-ONvz0DdhbMB96eTZDKA) |

## 3、功能Demo

我最近开源了这个库的全部源代码，功能正在开发中，欢迎大家参与开发~

- [演示代码](https://github.com/CoderWanFeng/PyOfficeRobot/tree/main/demo)


---

<p align="center" id='开源交流群-banner'>
<a target="_blank" href='https://cos.python-office.com/group%2Ffree-group.jpg'>
<img src="https://cos.python-office.com/python-office-qr.jpg" width="100%"/>
</a> 
</p>

## ⭐Star

[![Stargazers over time](https://starchart.cc/CoderWanFeng/PyOfficeRobot.svg)](https://starchart.cc/CoderWanFeng/PyOfficeRobot)

## 致谢项目

- [wxauto](https://github.com/cluic/wxauto)