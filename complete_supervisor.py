# -*- coding: UTF-8 -*-
'''
完整的群聊监督系统 - 最终集成版
集成消息收集、违规检测、学员问答、定时保存Excel、缓存管理等所有功能
'''

import sys
import os
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from loguru import logger

# 添加项目路径
sys.path.insert(0, '.')

try:
    import schedule
except ImportError:
    print("⚠️  缺少schedule库，正在安装...")
    os.system("pip install schedule")
    import schedule

try:
    import pandas as pd
except ImportError:
    print("⚠️  缺少pandas库，正在安装...")
    os.system("pip install pandas openpyxl")
    import pandas as pd

from PyOfficeRobot.core.monitor.group_monitor import GroupMonitor

class CompleteSupervisor:
    """完整的群聊监督系统 - 单群监督模式，支持多个候选群聊名称"""
    
    def __init__(self, candidate_group_names: List[str], my_username: str, 
                 save_interval_minutes: int = 30,
                 clear_cache_hours: int = 24,
                 zhipu_api_key: str = None):
        """
        初始化完整的群聊监督系统
        
        Args:
            candidate_group_names: 候选群聊名称列表（系统会自动找到真实的群聊名称）
            my_username: 我的用户名（用于问答功能）
            save_interval_minutes: 保存间隔（分钟）
            clear_cache_hours: 清除缓存间隔（小时）
            zhipu_api_key: 智谱AI API密钥
        """
        self.candidate_group_names = candidate_group_names if isinstance(candidate_group_names, list) else [candidate_group_names]
        self.my_username = my_username
        self.save_interval_minutes = save_interval_minutes
        self.clear_cache_hours = clear_cache_hours
        self.zhipu_api_key = zhipu_api_key
        
        # 单群监控器
        self.monitor = GroupMonitor(
            zhipu_api_key=zhipu_api_key,    # 传递AI API密钥用于问答功能
            enable_ai_analysis=False,       # 禁用AI违规检测
            enable_qa=True,                 # 启用问答功能
            enable_data_storage=True,       # 启用数据存储
            storage_path="./supervisor_data",  # 统一存储路径
            my_username=my_username,
            enable_violation_warning=False,  # 禁用内置警告，使用自定义发送
            candidate_group_names=self.candidate_group_names  # 传递候选群聊名称
        )
        
        # 当前实际使用的群聊名称（将在监控开始后确定）
        self.actual_group_name = None
        
        # 统一的消息记录存储
        self.unified_messages = []  # 存储所有群聊的消息记录
        self.violation_keywords_set = set()  # 违规关键词集合，用于快速检查
        
        # 统计信息
        self.stats = {
            "total_messages": 0,
            "violation_messages": 0,
            "qa_responses": 0,
            "last_save_time": None,
            "last_clear_time": None,
            "start_time": datetime.now(),
            "files_saved": []
        }
        
        # 设置预设问答
        self._setup_preset_qa()
        
        # 设置定时任务
        self._setup_scheduled_tasks()
        
        # 为监控器添加消息处理器和违规检测
        self.monitor.add_message_handler(self._message_handler)
        
        # 设置基础违规关键词检测
        self._setup_basic_violation_detection()
        
        logger.info("🚀 完整群聊监督系统初始化完成")
        print("🚀 完整群聊监督系统初始化完成")
        print(f"📱 候选群聊: {', '.join(self.candidate_group_names)} (共{len(self.candidate_group_names)}个)")
        print(f"🤖 问答用户: {my_username}")
        print(f"� 保存间隔:完 {save_interval_minutes}分钟")
        print(f"�️整 清缓存间隔: {clear_cache_hours}小时")
        print(f"🤖 AI功能: 已禁用")
        print(f"🚫 违规检测: 已启用（基础检测）")
        print(f"� 问答功能:  已启用")
        print(f"� 数据存储: 已启用c")
    
    def _setup_preset_qa(self):
        """设置预设问答"""
        preset_qa = {
            # 基础问候
            "你好": "你好！我是群聊学习助手，有什么学习问题可以帮助你的吗？",
            "hi": "Hi！有什么技术问题需要帮助吗？",
            "hello": "Hello！欢迎提问，我会尽力帮助你解决问题。",
            
            # 功能介绍
            "帮助": "我是学习助手，可以回答编程、开发、工具使用等问题。直接@我提问即可！",
            "功能": "我支持：技术问答、学习指导、代码解释、工具推荐、问题诊断等。",
            "监控": "我正在监控群聊，提供问答服务，定时保存数据。",
            
            # 学习相关
            "学习": "学习编程需要多练习、多思考。有具体的学习问题可以详细问我哦！",
            "入门": "编程入门建议：选择一门语言深入学习，多写代码，多看文档，遇到问题及时提问。",
            "资料": "推荐学习资源：官方文档、GitHub项目、技术博客、在线教程。有特定需求可以详细问我。",
            
            # 技术相关
            "python": "Python是很棒的编程语言！有什么Python相关问题可以详细问我。",
            "代码": "代码问题我很乐意帮忙！请贴出具体代码和错误信息，我来帮你分析。",
            "bug": "遇到bug不要慌！请描述具体现象和错误信息，我来帮你排查问题。",
            "报错": "报错信息很重要！请把完整的错误信息发出来，我帮你分析原因和解决方案。",
            
            # 系统状态
            "状态": f"系统运行正常，正在监控群聊并提供服务。启动时间：{datetime.now().strftime('%H:%M:%S')}",
            "统计": "可以查看消息统计、违规记录、问答历史等信息。",
            "时间": f"现在时间是：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            
            # 测试相关
            "测试": "✅ 系统测试成功！所有功能正常工作，包括问答、数据保存等。",
            "版本": "当前版本：基础监督系统 v4.0 - 专注于消息监控和问答功能。",
            
            # 感谢用语
            "谢谢": "不客气！能帮到你我很开心，有其他问题随时问我。",
            "感谢": "不用谢！学习路上互相帮助，有问题继续找我哦。",
        }
        
        # 预设问答将在后面为每个监控器单独设置
        self.preset_qa = preset_qa
        
        logger.info(f"✅ 已添加 {len(preset_qa)} 条预设问答")
        print(f"✅ 已添加 {len(preset_qa)} 条预设问答")
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        # 定时保存数据
        schedule.every(self.save_interval_minutes).minutes.do(self._scheduled_save)
        
        # 定时清除缓存
        schedule.every(self.clear_cache_hours).hours.do(self._scheduled_clear_cache)
        
        # 每天凌晨保存24小时数据
        schedule.every().day.at("00:01").do(self._save_daily_data)
        
        # 每小时保存一次实时数据（额外保险）
        schedule.every().hour.do(self._hourly_backup)
        
        logger.info("⏰ 定时任务已设置")
        print(f"⏰ 定时任务已设置:")
        print(f"   - 每{self.save_interval_minutes}分钟保存数据")
        print(f"   - 每{self.clear_cache_hours}小时清除缓存")
        print(f"   - 每天00:01保存24小时数据")
        print(f"   - 每小时备份实时数据")
    
    def _message_handler(self, message):
        """消息处理器"""
        self.stats["total_messages"] += 1
        
        # 显示消息信息
        timestamp = datetime.now().strftime("%H:%M:%S")
        msg_type = message.message_type.value
        
        print(f"[{timestamp}] 📨 {message.sender} ({msg_type}): {message.content}")
        
        # 添加到统一消息记录
        self._add_to_unified_messages(message)
        
        # 如果是@消息，统计问答次数（但要排除引用消息）
        if "@" in message.content and self.my_username in message.content:
            # 检查是否是引用消息，如果是则不统计问答次数
            if hasattr(self.monitor.qa_handler, '_contains_quoted_message') and \
               self.monitor.qa_handler._contains_quoted_message(message):
                print(f"  📝 跳过引用消息的@统计: [{message.sender}] {message.content[:50]}...")
            else:
                self.stats["qa_responses"] += 1
                print(f"  🤖 问答响应 (第{self.stats['qa_responses']}次)")
        
        # 检查是否是违规消息（单群监控器的违规记录）
        total_violations = len(self.monitor.get_violation_records())
        
        if total_violations > self.stats["violation_messages"]:
            self.stats["violation_messages"] = total_violations
            print(f"  🚨 违规检测 (第{self.stats['violation_messages']}次)")
    
    def _add_to_unified_messages(self, message):
        """添加消息到统一记录"""
        try:
            # 过滤系统消息和时间消息
            if message.message_type.value in ["system", "time"]:
                return
            
            # 检查是否违规
            is_violation = self._is_violation_message(message)
            
            # 创建统一消息记录
            unified_message = {
                "发言时间": message.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "群聊名称": message.group_name,
                "发言人": message.sender,
                "发言内容": message.content,
                "消息类型": message.message_type.value,
                "是否违规": "是" if is_violation else "否",
                "违规关键词": self._get_violation_keyword(message) if is_violation else "",
                "消息ID": message.id
            }
            
            # 添加到统一消息列表
            self.unified_messages.append(unified_message)
            
        except Exception as e:
            logger.error(f"添加统一消息记录失败: {e}")
    
    def _is_violation_message(self, message):
        """检查消息是否违规"""
        if message.message_type.value != "text":
            return False
        
        content_lower = message.content.lower()
        for keyword in self.violation_keywords:
            if keyword in content_lower:
                return True
        return False
    
    def _get_violation_keyword(self, message):
        """获取违规关键词"""
        if message.message_type.value != "text":
            return ""
        
        content_lower = message.content.lower()
        for keyword in self.violation_keywords:
            if keyword in content_lower:
                return keyword
        return ""
    
    def _scheduled_save(self):
        """定时保存数据"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\n💾 [{timestamp}] 执行定时保存...")
            
            # 保存单群监控器的实时数据
            realtime_file = self.monitor.save_realtime_data()
            
            if realtime_file:
                self.stats["last_save_time"] = datetime.now()
                self.stats["files_saved"].append({
                    "file": realtime_file,
                    "time": datetime.now(),
                    "type": "实时数据"
                })
                print(f"✅ 实时数据已保存: {realtime_file}")
                
                # 显示统计信息
                self._print_stats()
            else:
                print("❌ 实时数据保存失败")
                
        except Exception as e:
            logger.error(f"定时保存出错: {e}")
            print(f"❌ 定时保存出错: {e}")
    
    def _hourly_backup(self):
        """每小时备份"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\n🔄 [{timestamp}] 执行每小时备份...")
            
            # 保存所有群聊的实时数据作为备份
            backup_files = []
            for group_name, monitor in self.monitors.items():
                backup_file = monitor.save_realtime_data()
                if backup_file:
                    backup_files.append(backup_file)
            
            if backup_files:
                for file_path in backup_files:
                    self.stats["files_saved"].append({
                        "file": file_path,
                        "time": datetime.now(),
                        "type": "小时备份"
                    })
                print(f"✅ 小时备份完成: {len(backup_files)}个文件")
            else:
                print("❌ 小时备份失败")
                
        except Exception as e:
            logger.error(f"小时备份出错: {e}")
            print(f"❌ 小时备份出错: {e}")
    
    def _scheduled_clear_cache(self):
        """定时清除缓存"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\n🗑️ [{timestamp}] 执行缓存清理...")
            
            # 先保存所有群聊的当前数据
            backup_files = []
            for group_name, monitor in self.monitors.items():
                backup_file = monitor.save_realtime_data()
                if backup_file:
                    backup_files.append(backup_file)
            
            if backup_files:
                for file_path in backup_files:
                    self.stats["files_saved"].append({
                        "file": file_path,
                        "time": datetime.now(),
                        "type": "清缓存前备份"
                    })
                print(f"✅ 清缓存前数据已备份: {len(backup_files)}个文件")
            
            # 清除所有群聊的缓存
            for monitor in self.monitors.values():
                monitor.clear_data_cache()
                monitor.clear_qa_history()
            
            self.stats["last_clear_time"] = datetime.now()
            print("✅ 缓存已清理")
            
        except Exception as e:
            logger.error(f"缓存清理出错: {e}")
            print(f"❌ 缓存清理出错: {e}")
    
    def _save_daily_data(self):
        """保存24小时数据"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\n📊 [{timestamp}] 保存昨日24小时数据...")
            
            # 保存所有群聊昨天的数据
            yesterday = datetime.now() - timedelta(days=1)
            daily_files = []
            for group_name, monitor in self.monitors.items():
                daily_file = monitor.save_daily_data(yesterday)
                if daily_file:
                    daily_files.append(daily_file)
            
            if daily_files:
                for file_path in daily_files:
                    self.stats["files_saved"].append({
                        "file": file_path,
                        "time": datetime.now(),
                        "type": "24小时数据"
                    })
                print(f"✅ 昨日24小时数据已保存: {len(daily_files)}个文件")
            else:
                print("❌ 昨日24小时数据保存失败")
                
        except Exception as e:
            logger.error(f"24小时数据保存出错: {e}")
            print(f"❌ 24小时数据保存出错: {e}")
    
    def _print_stats(self):
        """打印统计信息"""
        runtime = datetime.now() - self.stats["start_time"]
        
        print(f"\n📊 运行统计:")
        print(f"   运行时间: {runtime}")
        print(f"   总消息数: {self.stats['total_messages']}")
        print(f"   违规消息: {self.stats['violation_messages']}")
        print(f"   问答次数: {self.stats['qa_responses']}")
        print(f"   已保存文件: {len(self.stats['files_saved'])}")
        
        if self.stats["last_save_time"]:
            print(f"   上次保存: {self.stats['last_save_time'].strftime('%H:%M:%S')}")
        
        if self.stats["last_clear_time"]:
            print(f"   上次清缓存: {self.stats['last_clear_time'].strftime('%H:%M:%S')}")
        
        # 获取所有监控器统计
        try:
            total_cache_messages = 0
            total_violations = 0
            total_qa_history = 0
            
            for monitor in self.monitors.values():
                monitor_stats = monitor.get_data_stats()
                if monitor_stats and "error" not in monitor_stats:
                    total_cache_messages += monitor_stats.get('消息数量', 0)
                total_violations += len(monitor.get_violation_records())
                total_qa_history += len(monitor.get_qa_history())
            
            print(f"   缓存消息: {total_cache_messages}")
            print(f"   违规记录: {total_violations}")
            print(f"   问答历史: {total_qa_history}")
        except Exception as e:
            logger.debug(f"获取监控器统计失败: {e}")
        
        print("-" * 40)
    
    def _setup_basic_violation_detection(self):
        """设置基础违规关键词检测"""
        # 定义违规关键词列表
        self.violation_keywords = [
            # 添加好友相关
            "加我微信", "加微信", "加我好友", "加好友", "私聊我", "私信我",
            "微信号", "wx号", "联系我", "找我私聊", "加我",
            
            # 转账相关
            "转账", "打钱", "付款", "支付",  "收费", "付费",
            "汇款", "银行卡", "支付宝", "微信支付",
            
            # 加群相关
            "加群", "进群", "群号", "拉群", "建群", "群聊",
            "扫码进群", "群二维码", "群链接",
            
            # 广告相关
            "推广", 
            "刷单", "网赚", "副业", "招聘"
        ]
        
        # 为监控器添加自定义消息处理器来检测违规
        self.monitor.add_message_handler(self._check_basic_violations)
        
        logger.info(f"✅ 已设置基础违规检测，监控 {len(self.violation_keywords)} 个关键词")
        print(f"✅ 已设置基础违规检测，监控 {len(self.violation_keywords)} 个关键词")
    
    def _check_basic_violations(self, message):
        """检查基础违规关键词"""
        try:
            # 只检查文本消息
            if message.message_type.value != "text":
                return
            
            # 检查消息内容是否包含违规关键词
            content_lower = message.content.lower()
            
            for keyword in self.violation_keywords:
                if keyword in content_lower:
                    # 创建违规记录
                    from PyOfficeRobot.core.monitor.models import ViolationRecord
                    
                    violation_record = ViolationRecord(
                        user_id=message.sender,
                        user_name=message.sender,
                        violation_type="关键词违规",
                        violation_content=message.content,
                        detection_time=datetime.now(),
                        group_name=message.group_name,
                        confidence=1.0,  # 关键词匹配置信度为100%
                        message_id=message.id
                    )
                    
                    # 添加违规记录到单群监控器
                    self.monitor.add_violation_record(violation_record)
                    
                    logger.warning(f"检测到违规关键词 '{keyword}': [{message.sender}] {message.content}")
                    print(f"  🚨 违规关键词: '{keyword}'")
                    
                    # 发送违规警告消息到消息来源的群聊
                    self._send_violation_warning_message(message.sender, message.group_name)
                    
                    # 只匹配第一个关键词就停止，避免重复记录
                    break
                    
        except Exception as e:
            logger.error(f"基础违规检测出错: {e}")
    
    def _send_violation_warning_message(self, user_name: str, group_name: str):
        """
        发送违规警告消息到指定群聊 - 改进版，支持群名变更后的消息发送
        
        Args:
            user_name: 违规用户名
            group_name: 目标群聊名称
        """
        try:
            # 生成警告消息
            warning_message = f"@{user_name} 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。"
            
            logger.info(f"准备发送违规警告到 {group_name}: {warning_message}")
            print(f"🚨 违规关键词: '转账'")
            
            # 使用监控器的改进消息发送方法
            success = self.monitor._send_message_to_current_chat(warning_message)
            
            if success:
                logger.info(f"✅ 违规警告发送成功到 {self.monitor.current_group}: {warning_message}")
                print(f"  📢 已发送警告到 {self.monitor.current_group}: @{user_name}")
            else:
                logger.warning(f"❌ 违规警告发送失败到 {self.monitor.current_group}: {warning_message}")
                print(f"  ❌ 警告发送失败到 {self.monitor.current_group}: @{user_name}")
                
                # 备用方法：尝试使用PyOfficeRobot API
                try:
                    import PyOfficeRobot
                    # 使用监控器的当前群聊名称，而不是传入的group_name
                    current_group = self.monitor.current_group if self.monitor.current_group else group_name
                    result = PyOfficeRobot.chat.send_message(who=current_group, message=warning_message)
                    
                    if result:
                        logger.info(f"✅ 备用方法发送成功到 {current_group}: {warning_message}")
                        print(f"  📢 备用方法已发送警告到 {current_group}: @{user_name}")
                    else:
                        logger.error(f"❌ 备用方法也发送失败到 {current_group}: {warning_message}")
                        print(f"  ❌ 备用方法发送失败到 {current_group}: @{user_name}")
                        
                except Exception as backup_error:
                    logger.error(f"备用发送方法出错: {backup_error}")
                    print(f"  ❌ 备用发送方法出错: {backup_error}")
                
        except Exception as e:
            logger.error(f"发送违规警告消息出错: {e}")
            print(f"  ❌ 发送警告出错: {e}")
    
    def get_file_list(self) -> List[Dict]:
        """获取已保存的文件列表"""
        return self.stats["files_saved"].copy()
    
    def manual_save(self) -> List[str]:
        """手动保存数据"""
        try:
            print("🔄 执行手动保存...")
            saved_files = []
            
            for group_name, monitor in self.monitors.items():
                filepath = monitor.save_realtime_data()
                if filepath:
                    saved_files.append(filepath)
                    self.stats["files_saved"].append({
                        "file": filepath,
                        "time": datetime.now(),
                        "type": "手动保存"
                    })
            
            if saved_files:
                print(f"✅ 手动保存成功: {len(saved_files)}个文件")
                return saved_files
            else:
                print("❌ 手动保存失败")
                return []
                
        except Exception as e:
            logger.error(f"手动保存出错: {e}")
            print(f"❌ 手动保存出错: {e}")
            return []
    
    def export_all_data(self) -> Dict[str, List[str]]:
        """导出所有数据"""
        try:
            print("📦 导出所有数据...")
            
            exported_files = {"realtime": [], "daily": []}
            
            # 导出所有群聊的实时数据
            for group_name, monitor in self.monitors.items():
                realtime_file = monitor.save_realtime_data()
                if realtime_file:
                    exported_files["realtime"].append(realtime_file)
                    print(f"✅ {group_name} 实时数据: {realtime_file}")
            
            # 导出所有群聊的今日数据
            for group_name, monitor in self.monitors.items():
                daily_file = monitor.save_daily_data()
                if daily_file:
                    exported_files["daily"].append(daily_file)
                    print(f"✅ {group_name} 今日数据: {daily_file}")
            
            # 记录导出的文件
            for file_type, file_list in exported_files.items():
                for filepath in file_list:
                    self.stats["files_saved"].append({
                        "file": filepath,
                        "time": datetime.now(),
                        "type": f"导出-{file_type}"
                    })
            
            total_files = len(exported_files["realtime"]) + len(exported_files["daily"])
            print(f"📦 数据导出完成，共{total_files}个文件")
            return exported_files
            
        except Exception as e:
            logger.error(f"导出数据出错: {e}")
            print(f"❌ 导出数据出错: {e}")
            return {"realtime": [], "daily": []}
    
    def save_unified_messages(self) -> str:
        """保存统一的消息记录表格"""
        try:
            if not self.unified_messages:
                logger.warning("没有统一消息数据可保存")
                return ""
            
            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"统一聊天记录_{timestamp}.xlsx"
            filepath = os.path.join("./supervisor_data", filename)
            
            # 确保目录存在
            os.makedirs("./supervisor_data", exist_ok=True)
            
            # 创建DataFrame
            df = pd.DataFrame(self.unified_messages)
            
            # 按时间排序
            df = df.sort_values("发言时间")
            
            # 生成统计汇总
            stats_data = self._generate_unified_statistics()
            stats_df = pd.DataFrame(stats_data)
            
            # 保存到Excel（多个工作表）
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 主要消息记录表
                df.to_excel(writer, sheet_name='聊天记录', index=False)
                
                # 统计汇总表
                stats_df.to_excel(writer, sheet_name='统计汇总', index=False)
                
                # 违规记录汇总
                violation_df = df[df['是否违规'] == '是'].copy()
                if not violation_df.empty:
                    violation_df.to_excel(writer, sheet_name='违规记录', index=False)
                
                # 按群聊分组统计
                group_stats = self._generate_group_statistics(df)
                group_stats.to_excel(writer, sheet_name='分群统计', index=False)
            
            logger.info(f"统一消息记录已保存到: {filepath}")
            logger.info(f"共保存 {len(self.unified_messages)} 条消息")
            
            # 记录到文件列表
            self.stats["files_saved"].append({
                "file": filepath,
                "time": datetime.now(),
                "type": "统一聊天记录"
            })
            
            return filepath
            
        except Exception as e:
            logger.error(f"保存统一消息记录失败: {e}")
            return ""
    
    def _generate_unified_statistics(self) -> List[Dict]:
        """生成统一统计数据"""
        stats_data = []
        
        if not self.unified_messages:
            return stats_data
        
        # 基本统计信息
        total_messages = len(self.unified_messages)
        violation_messages = len([msg for msg in self.unified_messages if msg['是否违规'] == '是'])
        
        # 时间范围
        if self.unified_messages:
            start_time = min(msg['发言时间'] for msg in self.unified_messages)
            end_time = max(msg['发言时间'] for msg in self.unified_messages)
        else:
            start_time = end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        stats_data.extend([
            {"统计项目": "监控时间范围", "统计值": f"{start_time} 至 {end_time}"},
            {"统计项目": "总消息数", "统计值": total_messages},
            {"统计项目": "违规消息数", "统计值": violation_messages},
            {"统计项目": "违规率", "统计值": f"{violation_messages/total_messages*100:.1f}%" if total_messages > 0 else "0%"},
            {"统计项目": "监控群聊数", "统计值": len(self.group_names)},
            {"统计项目": "监控群聊", "统计值": ", ".join(self.group_names)},
        ])
        
        # 按发言人统计
        user_counts = {}
        user_violations = {}
        for msg in self.unified_messages:
            user = msg["发言人"]
            user_counts[user] = user_counts.get(user, 0) + 1
            if msg["是否违规"] == "是":
                user_violations[user] = user_violations.get(user, 0) + 1
        
        stats_data.append({"统计项目": "活跃用户数", "统计值": len(user_counts)})
        
        # 添加分隔行
        stats_data.append({"统计项目": "--- 用户发言排行 ---", "统计值": ""})
        
        # 用户发言排行（前10名）
        sorted_users = sorted(user_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (user, count) in enumerate(sorted_users[:10]):
            violation_count = user_violations.get(user, 0)
            stats_data.append({
                "统计项目": f"第{i+1}名: {user}",
                "统计值": f"{count}条消息 (违规{violation_count}条)"
            })
        
        # 按群聊统计
        group_counts = {}
        group_violations = {}
        for msg in self.unified_messages:
            group = msg["群聊名称"]
            group_counts[group] = group_counts.get(group, 0) + 1
            if msg["是否违规"] == "是":
                group_violations[group] = group_violations.get(group, 0) + 1
        
        # 添加分隔行
        stats_data.append({"统计项目": "--- 群聊活跃度排行 ---", "统计值": ""})
        
        # 群聊活跃度排行
        sorted_groups = sorted(group_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (group, count) in enumerate(sorted_groups):
            violation_count = group_violations.get(group, 0)
            stats_data.append({
                "统计项目": f"第{i+1}名: {group}",
                "统计值": f"{count}条消息 (违规{violation_count}条)"
            })
        
        # 消息类型分布
        message_type_counts = {}
        for msg in self.unified_messages:
            msg_type = msg["消息类型"]
            message_type_counts[msg_type] = message_type_counts.get(msg_type, 0) + 1
        
        # 添加分隔行
        stats_data.append({"统计项目": "--- 消息类型分布 ---", "统计值": ""})
        
        for msg_type, count in message_type_counts.items():
            stats_data.append({
                "统计项目": f"{msg_type}消息",
                "统计值": f"{count}条"
            })
        
        return stats_data
    
    def _generate_group_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成分群聊统计"""
        group_stats = []
        
        for group_name in self.group_names:
            group_df = df[df['群聊名称'] == group_name]
            
            if not group_df.empty:
                total_messages = len(group_df)
                violation_messages = len(group_df[group_df['是否违规'] == '是'])
                unique_users = group_df['发言人'].nunique()
                
                # 最活跃用户
                top_user = group_df['发言人'].value_counts().index[0] if not group_df.empty else "无"
                top_user_count = group_df['发言人'].value_counts().iloc[0] if not group_df.empty else 0
                
                group_stats.append({
                    "群聊名称": group_name,
                    "总消息数": total_messages,
                    "违规消息数": violation_messages,
                    "违规率": f"{violation_messages/total_messages*100:.1f}%" if total_messages > 0 else "0%",
                    "活跃用户数": unique_users,
                    "最活跃用户": top_user,
                    "最活跃用户消息数": top_user_count
                })
            else:
                group_stats.append({
                    "群聊名称": group_name,
                    "总消息数": 0,
                    "违规消息数": 0,
                    "违规率": "0%",
                    "活跃用户数": 0,
                    "最活跃用户": "无",
                    "最活跃用户消息数": 0
                })
        
        return pd.DataFrame(group_stats)
    
    def start_supervision(self):
        """启动监督"""
        print(f"\n🎯 开始单群监督，候选群聊: {', '.join(self.candidate_group_names)} (共{len(self.candidate_group_names)}个)")
        print("💡 功能特性:")
        print("  ✅ 实时消息收集")
        print("  ✅ 基础违规检测（关键词匹配）")
        print("  ✅ 学员问答支持")
        print("  ✅ 定时数据保存")
        print("  ✅ 自动缓存管理")
        print("  ✅ 智能群聊名称查找")
        print("\n💡 操作说明:")
        print("  - 系统会自动找到真实的群聊名称并监控")
        print("  - 学员可以@你进行问答")
        print("  - 按 Ctrl+C 停止监督并查看最终统计")
        print("=" * 60)
        
        # 启动定时任务线程
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(1)
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        try:
            # 启动单群监控
            self._start_single_group_monitoring()
            
        except KeyboardInterrupt:
            print("\n⏹️ 监督已停止")
            self._final_report()
            
        except Exception as e:
            print(f"❌ 监督出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _start_single_group_monitoring(self):
        """启动单群监控"""
        print(f"🔄 启动单群监控...")
        
        # 使用第一个候选群聊名称作为目标
        target_group = self.candidate_group_names[0] if self.candidate_group_names else "test_demo1"
        
        print(f"🎯 目标群聊: {target_group}")
        print(f"📋 候选名称: {', '.join(self.candidate_group_names)}")
        
        # 启动监控（这会阻塞当前线程）
        success = self.monitor.start_monitoring(target_group, check_interval=3)
        
        if not success:
            print("❌ 监控启动失败")
        else:
            print("✅ 监控已启动")
    
    def _final_report(self):
        """最终报告"""
        print("\n" + "=" * 60)
        print("📊 最终监督报告")
        print("=" * 60)
        
        runtime = datetime.now() - self.stats["start_time"]
        print(f"运行时间: {runtime}")
        print(f"总消息数: {self.stats['total_messages']}")
        print(f"违规消息: {self.stats['violation_messages']}")
        print(f"问答次数: {self.stats['qa_responses']}")
        print(f"保存文件数: {len(self.stats['files_saved'])}")
        
        # 显示实际使用的群聊名称
        if self.monitor.current_group:
            print(f"实际监控群聊: {self.monitor.current_group}")
        
        # 保存最终统一数据
        if self.unified_messages:
            final_file = self.save_unified_messages()
            if final_file:
                print(f"最终数据已保存: {final_file}")
        
        print("=" * 60)
    
    def _start_multi_group_monitoring(self):
        """启动多群聊监控（多线程同时监控）"""
        print(f"🔄 启动多群聊同时监控...")
        
        # 首先检查所有群聊是否可用
        print(f"🔍 检查群聊可用性...")
        available_groups = self._check_group_availability()
        
        if not available_groups:
            print("❌ 没有可用的群聊，请检查群聊名称是否正确")
            return
        
        # 为每个可用群聊创建独立的监控线程
        monitor_threads = []
        
        for group_name in available_groups:
            print(f"🚀 启动 {group_name} 监控线程...")
            
            # 创建监控线程
            monitor_thread = threading.Thread(
                target=self._monitor_single_group,
                args=(group_name,),
                daemon=True,
                name=f"Monitor-{group_name}"
            )
            
            monitor_threads.append(monitor_thread)
            monitor_thread.start()
            
            # 短暂延迟，避免同时启动造成冲突
            time.sleep(0.5)
        
        print(f"✅ 已启动 {len(monitor_threads)} 个监控线程，正在同时监控所有群聊")
        print("📊 监控状态:")
        for group_name in available_groups:
            print(f"   🔍 {group_name} - 监控中")
        
        # 主线程等待，保持程序运行
        try:
            while True:
                # 检查所有线程是否还在运行
                active_threads = [t for t in monitor_threads if t.is_alive()]
                if not active_threads:
                    print("⚠️ 所有监控线程已停止")
                    break
                
                time.sleep(10)  # 每10秒检查一次线程状态
                
        except KeyboardInterrupt:
            print("\n⏹️ 收到停止信号，正在停止所有监控线程...")
            # 停止所有监控器
            for monitor in self.monitors.values():
                monitor.stop_monitoring()
            
            # 等待所有线程结束
            for thread in monitor_threads:
                if thread.is_alive():
                    thread.join(timeout=2)
            
            raise  # 重新抛出KeyboardInterrupt以触发_final_report
    
    def _check_group_availability(self) -> List[str]:
        """检查群聊可用性"""
        available_groups = []
        
        try:
            # 使用第一个监控器来检查群聊
            if not self.monitors:
                print("❌ 没有监控器可用")
                return []
            
            # 获取第一个监控器
            first_monitor = list(self.monitors.values())[0]
            
            # 获取会话列表
            print("📋 正在获取微信会话列表...")
            session_list = first_monitor.wx.GetSessionList()
            
            if not session_list:
                print("❌ 无法获取微信会话列表，请确保微信已登录")
                return []
            
            print(f"📋 找到 {len(session_list)} 个会话")
            print(f"📋 前10个会话: {session_list[:10]}")
            
            # 检查每个群聊是否在会话列表中
            for group_name in self.group_names:
                print(f"🔍 检查群聊: {group_name}")
                
                # 检查群聊名称是否在会话列表中
                if group_name in session_list:
                    print(f"✅ 群聊 '{group_name}' 在会话列表中")
                    available_groups.append(group_name)
                else:
                    print(f"❌ 群聊 '{group_name}' 不在会话列表中")
                    
                    # 尝试模糊匹配
                    similar_groups = [s for s in session_list if group_name in s or s in group_name]
                    if similar_groups:
                        print(f"💡 可能的相似群聊: {similar_groups[:3]}")
                    
                    # 尝试直接打开群聊测试
                    try:
                        result = first_monitor.wx.ChatWith(group_name)
                        if result:
                            print(f"✅ 群聊 '{group_name}' 可以直接打开")
                            
                            # 获取实际打开的群聊名称
                            time.sleep(1)  # 等待窗口稳定
                            actual_session_list = first_monitor.wx.GetSessionList()
                            if actual_session_list and len(actual_session_list) > 0:
                                actual_group_name = actual_session_list[0]  # 当前活跃的群聊
                                if actual_group_name != group_name:
                                    print(f"💡 实际打开的群聊是: '{actual_group_name}'")
                                    print(f"🔄 将使用实际群聊名称: '{actual_group_name}'")
                                    # 更新群聊名称映射
                                    if not hasattr(self, 'group_name_mapping'):
                                        self.group_name_mapping = {}
                                    self.group_name_mapping[group_name] = actual_group_name
                                    available_groups.append(actual_group_name)
                                else:
                                    available_groups.append(group_name)
                            else:
                                available_groups.append(group_name)
                        else:
                            print(f"❌ 群聊 '{group_name}' 无法打开")
                    except Exception as e:
                        print(f"❌ 测试打开群聊 '{group_name}' 时出错: {e}")
            
            print(f"\n📊 群聊可用性检查结果:")
            print(f"   总群聊数: {len(self.group_names)}")
            print(f"   可用群聊数: {len(available_groups)}")
            print(f"   可用群聊: {available_groups}")
            
            return available_groups
            
        except Exception as e:
            logger.error(f"检查群聊可用性时出错: {e}")
            print(f"❌ 检查群聊可用性时出错: {e}")
            return []
        
        if not available_groups:
            print("❌ 没有可用的群聊，请检查群聊名称是否正确")
            return
        
        if not available_groups:
            print("❌ 没有可用的群聊，请检查群聊名称是否正确")
            return
        
        # 为每个可用群聊创建独立的监控线程
        monitor_threads = []
        
        for group_name in self.group_names:
            print(f"🚀 启动 {group_name} 监控线程...")
            
            # 创建监控线程
            monitor_thread = threading.Thread(
                target=self._monitor_single_group,
                args=(group_name,),
                daemon=True,
                name=f"Monitor-{group_name}"
            )
            
            monitor_threads.append(monitor_thread)
            monitor_thread.start()
            
            # 短暂延迟，避免同时启动造成冲突
            time.sleep(0.5)
        
        print(f"✅ 已启动 {len(monitor_threads)} 个监控线程，正在同时监控所有群聊")
        print("📊 监控状态:")
        for group_name in self.group_names:
            print(f"   🔍 {group_name} - 监控中")
        
        # 主线程等待，保持程序运行
        try:
            while True:
                # 检查所有线程是否还在运行
                active_threads = [t for t in monitor_threads if t.is_alive()]
                if not active_threads:
                    print("⚠️ 所有监控线程已停止")
                    break
                
                time.sleep(10)  # 每10秒检查一次线程状态
                
        except KeyboardInterrupt:
            print("\n⏹️ 收到停止信号，正在停止所有监控线程...")
            # 停止所有监控器
            for monitor in self.monitors.values():
                monitor.stop_monitoring()
            
            # 等待所有线程结束
            for thread in monitor_threads:
                if thread.is_alive():
                    thread.join(timeout=2)
            
            raise  # 重新抛出KeyboardInterrupt以触发_final_report
    
    def _monitor_single_group(self, group_name: str):
        """单个群聊的监控线程函数"""
        try:
            print(f"🔍 开始监控群聊: {group_name}")
            
            # 获取对应的监控器
            monitor = self.monitors[group_name]
            
            # 设置当前活跃群聊（用于发送警告消息）
            self.current_active_group = group_name
            
            # 启动监控（这会阻塞当前线程）
            monitor.start_monitoring(group_name, check_interval=3)
            
        except Exception as e:
            logger.error(f"群聊 {group_name} 监控线程出错: {e}")
            print(f"❌ 群聊 {group_name} 监控出错: {e}")
        finally:
            print(f"⏹️ 群聊 {group_name} 监控线程已停止")
    
    def _final_report(self):
        """最终报告"""
        print("\n" + "=" * 60)
        print("📋 最终监督报告")
        print("=" * 60)
        
        # 保存最终数据
        print("💾 保存最终数据...")
        final_realtime_files = []
        final_daily_files = []
        
        # 保存统一聊天记录表格
        unified_file = self.save_unified_messages()
        if unified_file:
            print(f"✅ 统一聊天记录: {unified_file}")
        
        for group_name, monitor in self.monitors.items():
            realtime_file = monitor.save_realtime_data()
            daily_file = monitor.save_daily_data()
            
            if realtime_file:
                final_realtime_files.append(realtime_file)
                print(f"✅ {group_name} 实时数据: {realtime_file}")
            if daily_file:
                final_daily_files.append(daily_file)
                print(f"✅ {group_name} 今日数据: {daily_file}")
        
        # 显示详细统计
        self._print_stats()
        
        # 显示问答历史（合并所有群聊）
        all_qa_history = []
        for monitor in self.monitors.values():
            qa_history = monitor.get_qa_history()
            all_qa_history.extend(qa_history)
        
        if all_qa_history:
            # 按时间排序
            all_qa_history.sort(key=lambda x: x['时间'], reverse=True)
            print(f"\n🤖 问答历史 (共{len(all_qa_history)}次):")
            for i, qa in enumerate(all_qa_history[:5], 1):  # 显示最后5次
                print(f"{i}. [{qa['提问者']}] {qa['问题']}")
                print(f"   回答: {qa['答案'][:50]}...")
        
        # 显示违规记录（合并所有群聊）
        all_violations = []
        for monitor in self.monitors.values():
            violations = monitor.get_violation_records()
            all_violations.extend(violations)
        
        if all_violations:
            # 按时间排序
            all_violations.sort(key=lambda x: x.detection_time, reverse=True)
            print(f"\n🚨 违规记录 (共{len(all_violations)}次):")
            for i, violation in enumerate(all_violations[:3], 1):  # 显示最后3次
                print(f"{i}. [{violation.user_name}] {violation.violation_type}")
                print(f"   内容: {violation.violation_content[:30]}...")
        
        print(f"\n🎉 多群聊监督完成！共监控了{len(self.group_names)}个群聊")

def main():
    """主函数"""
    print("🚀 完整群聊监督系统 - 单群监督模式")
    print("=" * 60)
    
    # 获取配置
    print("📋 系统配置:")
    
    # 支持多个候选群聊名称输入
    print("请输入候选群聊名称（多个名称用逗号分隔，系统会自动找到真实的群聊）:")
    print("💡 提示：群聊名称可能会变化，提供多个候选名称可以提高匹配成功率")
    group_input = input("候选群聊名称: ").strip()
    if not group_input:
        print("❌ 未输入群聊名称")
        return
    
    # 解析候选群聊名称列表
    candidate_group_names = [name.strip() for name in group_input.split(',') if name.strip()]
    if not candidate_group_names:
        print("❌ 未输入有效的群聊名称")
        return
    print(f"候选群聊名称列表: {candidate_group_names}")
    
    my_username = input("请输入你的微信昵称（用于问答功能）: ").strip()
    if not my_username:
        print("❌ 未输入用户名")
        return
    
    # 可选配置
    save_interval = input("保存间隔（分钟，默认30）: ").strip()
    save_interval = int(save_interval) if save_interval.isdigit() else 30
    
    clear_interval = input("清缓存间隔（小时，默认24）: ").strip()
    clear_interval = int(clear_interval) if clear_interval.isdigit() else 24
    
    # AI API配置（写死在代码中）
    api_key = "5fe933a8186a410eba8171e71248e6d9.ZZbSscsop8f2Riky"
    
    # 创建监督系统
    supervisor = CompleteSupervisor(
        candidate_group_names=candidate_group_names,
        my_username=my_username,
        save_interval_minutes=save_interval,
        clear_cache_hours=clear_interval,
        zhipu_api_key=api_key
    )
    
    # 启动监督
    supervisor.start_supervision()

if __name__ == "__main__":
    main()