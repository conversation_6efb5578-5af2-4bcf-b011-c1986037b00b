#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试引用消息过滤功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

try:
    from PyOfficeRobot.core.monitor.models import Message, MessageType
    from PyOfficeRobot.core.monitor.qa_handler import QAHandler
    print("✅ 模块导入成功")
    
    # 创建问答处理器实例
    qa_handler = QAHandler(
        my_username="The end transformer",
        enable_auto_reply=False,  # 测试时不自动回复
        candidate_group_names=["test_demo1"]
    )
    print("✅ QAHandler 实例创建成功")
    
    # 测试用例
    test_cases = [
        {
            "name": "正常@消息",
            "content": "@The end transformer 你好，请问Python怎么学习？",
            "should_process": True
        },
        {
            "name": "包含引用关键词的@消息",
            "content": "@The end transformer 引用的消息：Python很难学吗？",
            "should_process": False
        },
        {
            "name": "包含回复关键词的@消息", 
            "content": "@The end transformer 回复了：我觉得可以试试",
            "should_process": False
        },
        {
            "name": "包含微信引用符号的@消息",
            "content": "@The end transformer 「这是引用的内容」我想问一下",
            "should_process": False
        },
        {
            "name": "包含引用格式的@消息",
            "content": "@The end transformer 用户名: 原始消息内容\n我的问题是什么？",
            "should_process": False
        },
        {
            "name": "包含>符号的@消息",
            "content": "@The end transformer >> 引用内容\n我想问个问题",
            "should_process": False
        },
        {
            "name": "正常@消息（无引用）",
            "content": "@The end transformer 请帮我解释一下这个代码",
            "should_process": True
        },
        {
            "name": "包含引用但不@我的消息",
            "content": "引用的消息：这是一个测试",
            "should_process": False  # 因为没有@我
        }
    ]
    
    print("\n🧪 开始测试引用消息过滤功能:")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"消息内容: {test_case['content']}")
        
        # 创建测试消息
        test_message = Message(
            id=f"test_{i:03d}",
            sender="测试用户",
            content=test_case['content'],
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("测试用户", test_case['content'], f"test_{i:03d}")
        )
        
        # 测试是否@了我
        is_mentioned = qa_handler.is_mentioned(test_message)
        print(f"是否@了我: {is_mentioned}")
        
        if is_mentioned:
            # 测试是否包含引用消息
            contains_quote = qa_handler._contains_quoted_message(test_message)
            print(f"包含引用消息: {contains_quote}")
            
            # 测试完整的问答处理
            will_process = qa_handler.handle_question(test_message)
            print(f"是否处理问答: {will_process}")
            
            # 验证结果
            expected = test_case['should_process']
            if will_process == expected:
                print(f"✅ 测试通过 (期望: {expected}, 实际: {will_process})")
            else:
                print(f"❌ 测试失败 (期望: {expected}, 实际: {will_process})")
        else:
            # 如果没有@我，应该不处理
            if not test_case['should_process']:
                print("✅ 测试通过 (没有@我，正确跳过)")
            else:
                print("❌ 测试失败 (应该@我但没有检测到)")
        
        print("-" * 40)
    
    print("\n🎉 引用消息过滤功能测试完成")
    
    # 显示问答历史（应该只包含非引用的@消息）
    qa_history = qa_handler.get_qa_history()
    print(f"\n📊 问答历史记录数量: {len(qa_history)}")
    for i, qa in enumerate(qa_history, 1):
        print(f"{i}. [{qa['提问者']}] {qa['问题'][:30]}...")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
