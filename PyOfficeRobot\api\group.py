#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群聊监控API
提供简单易用的群聊监控和QA功能接口
"""

import os
import time
from typing import Dict, List, Optional, Callable
from loguru import logger

from ..core.monitor.group_monitor import GroupMonitor


class WeChatGroupMonitor:
    """微信群聊监控器 - 集成API"""
    
    def __init__(self):
        self.monitor = None
        self.is_running = False
        
    def start_qa_bot(self, 
                     group_name: str,
                     my_username: str,
                     zhipu_api_key: str = "5fe933a8186a410eba8171e71248e6d9.ZZbSscsop8f2Riky",
                     preset_qa: Dict[str, str] = None,
                     check_interval: int = 2,
                     enable_data_storage: bool = True,
                     storage_path: str = "./monitor_data") -> bool:
        """
        启动QA机器人
        
        Args:
            group_name: 群聊名称
            my_username: 你的微信昵称
            zhipu_api_key: 智谱AI API密钥
            preset_qa: 预设问答字典
            check_interval: 消息检查间隔（秒）
            enable_data_storage: 是否启用数据存储
            storage_path: 数据存储路径
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 创建监控器
            self.monitor = GroupMonitor(
                zhipu_api_key=zhipu_api_key,
                enable_ai_analysis=False,  # 专注QA功能
                enable_qa=True,
                enable_data_storage=enable_data_storage,
                storage_path=storage_path,
                my_username=my_username,
                enable_violation_warning=False
            )
            
            logger.info(f"初始化QA机器人: 群聊={group_name}, 用户={my_username}")
            
            # 添加预设问答
            if preset_qa:
                for keyword, answer in preset_qa.items():
                    self.monitor.add_preset_qa(keyword, answer)
                    logger.debug(f"添加预设问答: {keyword}")
            
            # 添加默认问答
            default_qa = {
                "你好": "你好！我是群聊助手，有什么问题可以问我。",
                "时间": f"现在时间是：{time.strftime('%Y-%m-%d %H:%M:%S')}",
                "帮助": "我可以回答各种问题，直接@我提问即可！支持技术问答、学习指导等。",
                "功能": "我支持：技术问答、学习指导、代码解释、工具推荐、问题诊断等。",
            }
            
            for keyword, answer in default_qa.items():
                if not preset_qa or keyword not in preset_qa:
                    self.monitor.add_preset_qa(keyword, answer)
            
            # 启动监控
            logger.info("启动群聊监控...")
            self.is_running = True
            self.monitor.start_monitoring(group_name, check_interval)
            
            return True
            
        except Exception as e:
            logger.error(f"启动QA机器人失败: {e}")
            return False
    
    def start_violation_monitor(self,
                               group_name: str,
                               my_username: str,
                               zhipu_api_key: str = "5fe933a8186a410eba8171e71248e6d9.ZZbSscsop8f2Riky",
                               warning_template: str = "@{user} 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。",
                               check_interval: int = 2,
                               enable_data_storage: bool = True,
                               storage_path: str = "./monitor_data") -> bool:
        """
        启动违规监控
        
        Args:
            group_name: 群聊名称
            my_username: 你的微信昵称
            zhipu_api_key: 智谱AI API密钥
            warning_template: 警告消息模板
            check_interval: 消息检查间隔（秒）
            enable_data_storage: 是否启用数据存储
            storage_path: 数据存储路径
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 创建监控器
            self.monitor = GroupMonitor(
                zhipu_api_key=zhipu_api_key,
                enable_ai_analysis=True,  # 启用AI违规分析
                enable_qa=False,
                enable_data_storage=enable_data_storage,
                storage_path=storage_path,
                my_username=my_username,
                enable_violation_warning=True
            )
            
            logger.info(f"初始化违规监控: 群聊={group_name}, 用户={my_username}")
            
            # 设置警告模板
            self.monitor.set_violation_warning_template(warning_template)
            
            # 启动监控
            logger.info("启动违规监控...")
            self.is_running = True
            self.monitor.start_monitoring(group_name, check_interval)
            
            return True
            
        except Exception as e:
            logger.error(f"启动违规监控失败: {e}")
            return False
    
    def start_full_monitor(self,
                          group_name: str,
                          my_username: str,
                          zhipu_api_key: str = "5fe933a8186a410eba8171e71248e6d9.ZZbSscsop8f2Riky",
                          preset_qa: Dict[str, str] = None,
                          warning_template: str = "@{user} 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。",
                          check_interval: int = 2,
                          enable_data_storage: bool = True,
                          storage_path: str = "./monitor_data") -> bool:
        """
        启动完整监控（QA + 违规检测）
        
        Args:
            group_name: 群聊名称
            my_username: 你的微信昵称
            zhipu_api_key: 智谱AI API密钥
            preset_qa: 预设问答字典
            warning_template: 警告消息模板
            check_interval: 消息检查间隔（秒）
            enable_data_storage: 是否启用数据存储
            storage_path: 数据存储路径
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 创建监控器
            self.monitor = GroupMonitor(
                zhipu_api_key=zhipu_api_key,
                enable_ai_analysis=True,   # 启用AI违规分析
                enable_qa=True,           # 启用QA功能
                enable_data_storage=enable_data_storage,
                storage_path=storage_path,
                my_username=my_username,
                enable_violation_warning=True
            )
            
            logger.info(f"初始化完整监控: 群聊={group_name}, 用户={my_username}")
            
            # 添加预设问答
            if preset_qa:
                for keyword, answer in preset_qa.items():
                    self.monitor.add_preset_qa(keyword, answer)
                    logger.debug(f"添加预设问答: {keyword}")
            
            # 添加默认问答
            default_qa = {
                "你好": "你好！我是群聊助手，有什么问题可以问我。",
                "时间": f"现在时间是：{time.strftime('%Y-%m-%d %H:%M:%S')}",
                "帮助": "我可以回答各种问题，直接@我提问即可！支持技术问答、学习指导等。",
                "功能": "我支持：技术问答、学习指导、代码解释、工具推荐、问题诊断等。",
                "监控": "我正在监控群聊，提供问答服务和违规检测功能。",
            }
            
            for keyword, answer in default_qa.items():
                if not preset_qa or keyword not in preset_qa:
                    self.monitor.add_preset_qa(keyword, answer)
            
            # 设置警告模板
            self.monitor.set_violation_warning_template(warning_template)
            
            # 启动监控
            logger.info("启动完整监控...")
            self.is_running = True
            self.monitor.start_monitoring(group_name, check_interval)
            
            return True
            
        except Exception as e:
            logger.error(f"启动完整监控失败: {e}")
            return False
    
    def stop_monitor(self):
        """停止监控"""
        if self.monitor and self.is_running:
            self.monitor.stop_monitoring()
            self.is_running = False
            logger.info("监控已停止")
    
    def get_status(self) -> Dict:
        """获取监控状态"""
        if not self.monitor:
            return {"status": "未初始化"}
        
        return self.monitor.get_monitoring_status()
    
    def get_qa_history(self) -> List[Dict]:
        """获取问答历史"""
        if not self.monitor:
            return []
        
        return self.monitor.get_qa_history()
    
    def get_violation_records(self) -> List:
        """获取违规记录"""
        if not self.monitor:
            return []
        
        return self.monitor.get_violation_records()
    
    def add_preset_qa(self, keyword: str, answer: str):
        """添加预设问答"""
        if self.monitor:
            self.monitor.add_preset_qa(keyword, answer)
            logger.info(f"添加预设问答: {keyword}")
    
    def save_daily_data(self, target_date=None) -> str:
        """保存日常数据"""
        if not self.monitor:
            return ""
        
        return self.monitor.save_daily_data(target_date)
    
    def save_realtime_data(self) -> str:
        """保存实时数据"""
        if not self.monitor:
            return ""
        
        return self.monitor.save_realtime_data()


# 创建全局实例
group_monitor = WeChatGroupMonitor()


# 便捷函数
def start_qa_bot(group_name: str, my_username: str, **kwargs) -> bool:
    """启动QA机器人的便捷函数"""
    return group_monitor.start_qa_bot(group_name, my_username, **kwargs)


def start_violation_monitor(group_name: str, my_username: str, **kwargs) -> bool:
    """启动违规监控的便捷函数"""
    return group_monitor.start_violation_monitor(group_name, my_username, **kwargs)


def start_full_monitor(group_name: str, my_username: str, **kwargs) -> bool:
    """启动完整监控的便捷函数"""
    return group_monitor.start_full_monitor(group_name, my_username, **kwargs)


def stop_monitor():
    """停止监控的便捷函数"""
    group_monitor.stop_monitor()


def get_status() -> Dict:
    """获取状态的便捷函数"""
    return group_monitor.get_status()


def get_qa_history() -> List[Dict]:
    """获取问答历史的便捷函数"""
    return group_monitor.get_qa_history()


def get_violation_records() -> List:
    """获取违规记录的便捷函数"""
    return group_monitor.get_violation_records()


def add_preset_qa(keyword: str, answer: str):
    """添加预设问答的便捷函数"""
    group_monitor.add_preset_qa(keyword, answer)


def save_daily_data(target_date=None) -> str:
    """保存日常数据的便捷函数"""
    return group_monitor.save_daily_data(target_date)


def save_realtime_data() -> str:
    """保存实时数据的便捷函数"""
    return group_monitor.save_realtime_data()