#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
缓存管理工具
提供缓存查看、清理、备份等功能
'''

import sys
import os
sys.path.insert(0, '.')

import PyOfficeRobot
from datetime import datetime
import json

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, storage_path: str = "./monitor_data"):
        """
        初始化缓存管理器
        
        Args:
            storage_path: 存储路径
        """
        self.storage_path = storage_path
        
        # 创建监控器用于缓存操作
        self.monitor = PyOfficeRobot.group.create_group_monitor(
            enable_data_storage=True,
            storage_path=storage_path,
            enable_qa=True,
            my_username="缓存管理器"
        )
        
        print(f"🗂️ 缓存管理器初始化完成")
        print(f"📁 存储路径: {storage_path}")
    
    def show_cache_stats(self):
        """显示缓存统计"""
        print("\n📊 缓存统计信息")
        print("-" * 30)
        
        try:
            stats = self.monitor.get_data_stats()
            
            if "error" in stats:
                print(f"❌ {stats['error']}")
                return
            
            for key, value in stats.items():
                print(f"  {key}: {value}")
            
            # 显示问答历史统计
            qa_history = self.monitor.get_qa_history()
            print(f"  问答历史数量: {len(qa_history)}")
            
            # 显示违规记录统计
            violations = self.monitor.get_violation_records()
            print(f"  违规记录数量: {len(violations)}")
            
        except Exception as e:
            print(f"❌ 获取缓存统计失败: {e}")
    
    def backup_cache(self, backup_name: str = None):
        """备份缓存数据"""
        if not backup_name:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"\n💾 备份缓存数据: {backup_name}")
        print("-" * 30)
        
        try:
            # 保存实时数据作为备份
            filepath = self.monitor.save_realtime_data()
            
            if filepath:
                # 重命名文件包含备份标识
                import shutil
                backup_path = filepath.replace('.xlsx', f'_{backup_name}.xlsx')
                shutil.move(filepath, backup_path)
                
                print(f"✅ 缓存已备份到: {backup_path}")
                return backup_path
            else:
                print("❌ 备份失败")
                return None
                
        except Exception as e:
            print(f"❌ 备份出错: {e}")
            return None
    
    def clear_cache(self, confirm: bool = False):
        """清空缓存"""
        if not confirm:
            response = input("\n⚠️ 确定要清空所有缓存吗？(y/N): ").strip().lower()
            if response != 'y':
                print("❌ 取消清空操作")
                return False
        
        print("\n🗑️ 清空缓存数据")
        print("-" * 30)
        
        try:
            # 先备份
            backup_path = self.backup_cache("before_clear")
            
            if backup_path:
                print(f"✅ 清空前已自动备份")
            
            # 清空数据缓存
            self.monitor.clear_data_cache()
            
            # 清空问答历史
            self.monitor.clear_qa_history()
            
            print("✅ 缓存已清空")
            return True
            
        except Exception as e:
            print(f"❌ 清空缓存出错: {e}")
            return False
    
    def export_cache_report(self):
        """导出缓存报告"""
        print("\n📋 导出缓存报告")
        print("-" * 30)
        
        try:
            # 获取统计信息
            stats = self.monitor.get_data_stats()
            qa_history = self.monitor.get_qa_history()
            violations = self.monitor.get_violation_records()
            
            # 创建报告
            report = {
                "生成时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "缓存统计": stats,
                "问答历史数量": len(qa_history),
                "违规记录数量": len(violations),
                "最近问答": qa_history[-5:] if qa_history else [],
                "最近违规": [
                    {
                        "用户": v.user_name,
                        "类型": v.violation_type,
                        "内容": v.violation_content[:50],
                        "时间": v.detection_time.strftime("%Y-%m-%d %H:%M:%S")
                    } for v in violations[-5:]
                ] if violations else []
            }
            
            # 保存报告
            report_file = f"{self.storage_path}/cache_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 报告已保存到: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ 导出报告出错: {e}")
            return None
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "=" * 40)
            print("🗂️ 缓存管理工具")
            print("=" * 40)
            print("1. 查看缓存统计")
            print("2. 备份缓存数据")
            print("3. 清空缓存")
            print("4. 导出缓存报告")
            print("5. 退出")
            print("-" * 40)
            
            choice = input("请选择操作 (1-5): ").strip()
            
            if choice == "1":
                self.show_cache_stats()
                
            elif choice == "2":
                backup_name = input("输入备份名称（留空自动生成）: ").strip()
                self.backup_cache(backup_name if backup_name else None)
                
            elif choice == "3":
                self.clear_cache()
                
            elif choice == "4":
                self.export_cache_report()
                
            elif choice == "5":
                print("👋 退出缓存管理工具")
                break
                
            else:
                print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🚀 缓存管理工具")
    print("=" * 40)
    
    # 获取存储路径
    storage_path = input("请输入存储路径（留空使用默认）: ").strip()
    if not storage_path:
        storage_path = "./monitor_data"
    
    # 创建缓存管理器
    cache_manager = CacheManager(storage_path)
    
    # 启动交互式菜单
    cache_manager.interactive_menu()

if __name__ == "__main__":
    main()