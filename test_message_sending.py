#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试消息发送功能
"""

import time
from PyOfficeRobot.core.monitor.qa_handler import QAHandler
from PyOfficeRobot.core.monitor.models import Message, MessageType
from PyOfficeRobot.core.WeChatType import WeChat

def test_message_sending():
    """测试消息发送功能"""
    print("开始测试消息发送功能...")
    
    try:
        # 初始化微信客户端
        wx = WeChat()
        print("微信客户端初始化成功")
        
        # 初始化QA处理器
        qa_handler = QAHandler(
            my_username="The end",  # 替换为你的微信昵称
            enable_auto_reply=True,
            reply_delay=1
        )
        qa_handler.set_wechat_client(wx)
        print("QA处理器初始化成功")
        
        # 创建测试消息
        test_message = Message(
            sender="测试用户",
            content="@The end 这是一条测试消息",
            timestamp=time.time(),
            message_type=MessageType.TEXT,
            group_name="测试群"
        )
        
        print(f"创建测试消息: {test_message.content}")
        
        # 测试消息处理
        print("开始处理测试消息...")
        result = qa_handler.handle_question(test_message)
        
        if result:
            print("✅ 消息处理成功")
        else:
            print("❌ 消息处理失败")
            
        # 等待一段时间观察结果
        print("等待5秒观察结果...")
        time.sleep(5)
        
        # 测试直接发送消息
        print("测试直接发送消息...")
        test_reply = "@测试用户 这是一条直接发送的测试回复"
        success = qa_handler._send_message_with_retry(test_reply)
        
        if success:
            print("✅ 直接发送消息成功")
        else:
            print("❌ 直接发送消息失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 50)
    print("微信群聊监控 - 消息发送功能测试")
    print("=" * 50)
    
    # 提示用户准备
    print("请确保:")
    print("1. 微信已经登录")
    print("2. 已经打开要测试的群聊窗口")
    print("3. 群聊窗口处于活动状态")
    print()
    
    input("准备就绪后按回车键开始测试...")
    
    test_message_sending()
    
    print("\n测试完成！")