#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试增强的引用消息检测功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

def test_enhanced_quote_detection():
    """测试增强的引用消息检测功能"""
    
    # 模拟消息类
    class MockMessage:
        def __init__(self, content):
            self.content = content
            self.sender = "测试用户"
            self.message_type = MockMessageType()
            self.timestamp = datetime.now()
            self.group_name = "test_demo1"
    
    class MockMessageType:
        def __init__(self):
            self.value = "text"
    
    # 引用消息检测函数（从group_monitor.py复制）
    def is_quoted_message(message):
        """检测消息是否为引用消息"""
        try:
            if message.message_type.value != "text":
                return False
            
            content = message.content.lower()
            
            # 检测引用消息的关键词（更严格的检测）
            quote_keywords = [
                "引用的消息",
                "引用  的消息",  # 特殊格式，包含多个空格
                "引用 的消息",   # 包含一个空格
                "引用消息",
                "回复了",
                "回复:",
                "引用:",
                "引用了",
                "「",  # 微信引用消息常用的符号
                "」",
                "『",
                "』",
                ">>",  # 常见的引用符号
                ">>>",
                "回复 ",
                "引用 ",
                "re:",
                "reply:",
                "quoted:",
            ]
            
            # 检查是否包含引用关键词
            for keyword in quote_keywords:
                if keyword in content:
                    return True
            
            # 检测微信引用消息的特殊格式模式
            import re
            
            # 模式1: 包含换行符且包含引用特征
            lines = message.content.split('\n')
            if len(lines) > 1:
                # 检查是否有"引用"相关的行
                for line in lines:
                    line_lower = line.lower().strip()
                    if any(keyword in line_lower for keyword in ["引用", "回复", "："]):
                        return True
            
            # 模式2: 消息开头包含特殊符号组合
            quote_patterns = [
                r'^「.*?」',  # 「引用内容」
                r'^『.*?』',  # 『引用内容』
                r'^>+\s*',   # > 或 >> 开头
                r'^\[.*?\]', # [引用内容]
            ]
            
            for pattern in quote_patterns:
                if re.search(pattern, message.content):
                    return True
            
            return False
            
        except Exception as e:
            print(f"检测引用消息失败: {e}")
            return False
    
    # 测试用例（包含实际的引用消息格式）
    test_cases = [
        ("@The end transformer 你好，请问Python怎么学习？", False, "正常@消息"),
        ("@The end transformer 引用的消息：Python很难学吗？", True, "包含引用关键词"),
        ("@The end 好几节课\n引用  的消息 : @The end nnn啥叫我看哇", True, "实际的引用消息格式"),
        ("@The end transformer 回复了：我觉得可以试试", True, "包含回复关键词"),
        ("@The end transformer 「这是引用的内容」我想问一下", True, "包含微信引用符号"),
        ("@The end transformer 用户名: 原始消息内容\n我的问题是什么？", True, "包含引用格式"),
        ("@The end transformer >> 引用内容\n我想问个问题", True, "包含>符号"),
        ("@The end transformer 请帮我解释一下这个代码", False, "正常@消息（无引用）"),
        ("普通消息，没有@", False, "普通消息"),
        ("@The end 引用 的消息：测试", True, "包含引用空格格式"),
    ]
    
    print("🧪 开始测试增强的引用消息检测功能:")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (content, expected, description) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {description}")
        print(f"消息内容: {content}")
        
        message = MockMessage(content)
        result = is_quoted_message(message)
        
        print(f"检测结果: {result}")
        print(f"期望结果: {expected}")
        
        if result == expected:
            print("✅ 测试通过")
            success_count += 1
        else:
            print("❌ 测试失败")
        
        print("-" * 40)
    
    print(f"\n🎉 测试完成！")
    print(f"📊 测试结果: {success_count}/{total_count} 通过")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎯 所有测试都通过了！引用消息检测功能工作正常。")
    else:
        print("⚠️ 有测试失败，需要进一步优化检测逻辑。")

if __name__ == "__main__":
    test_enhanced_quote_detection()
