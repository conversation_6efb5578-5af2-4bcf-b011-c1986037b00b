#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
简单测试引用消息过滤功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

def test_quote_detection():
    """测试引用消息检测功能"""
    
    # 模拟消息类
    class MockMessage:
        def __init__(self, content):
            self.content = content
            self.sender = "测试用户"
            self.message_type = "TEXT"
            self.timestamp = datetime.now()
            self.group_name = "test_demo1"
    
    # 模拟日志
    class MockLogger:
        def info(self, msg):
            print(f"INFO: {msg}")
        def error(self, msg):
            print(f"ERROR: {msg}")
    
    # 引用消息检测函数（从qa_handler.py复制）
    def contains_quoted_message(message):
        """检测消息是否包含引用的消息"""
        try:
            content = message.content.lower()
            
            # 检测引用消息的关键词
            quote_keywords = [
                "引用的消息",
                "引用消息", 
                "回复了",
                "回复:",
                "引用:",
                "引用了",
                "「",  # 微信引用消息常用的符号
                "」",
                "『",
                "』",
                ">>",  # 常见的引用符号
                ">>>",
                "回复 ",
                "引用 ",
                "re:",
                "reply:",
                "quoted:",
            ]
            
            # 检查是否包含引用关键词
            for keyword in quote_keywords:
                if keyword in content:
                    print(f"检测到引用消息关键词: '{keyword}' 在消息中: {message.content[:50]}...")
                    return True
            
            # 检测微信引用消息的特殊格式模式
            import re
            
            # 模式1: "用户名: 消息内容" 格式（常见的引用格式）
            if re.search(r'^[^:]+:\s*.+', content):
                print(f"检测到引用消息格式模式1: {message.content[:50]}...")
                return True
            
            # 模式2: 包含换行符且第一行较短（可能是引用的原消息）
            lines = message.content.split('\n')
            if len(lines) > 1 and len(lines[0]) < 50 and len(lines[0]) > 5:
                print(f"检测到引用消息格式模式2: {message.content[:50]}...")
                return True
            
            # 模式3: 消息开头包含特殊符号组合
            quote_patterns = [
                r'^「.*?」',  # 「引用内容」
                r'^『.*?』',  # 『引用内容』
                r'^>+\s*',   # > 或 >> 开头
                r'^\[.*?\]', # [引用内容]
            ]
            
            for pattern in quote_patterns:
                if re.search(pattern, message.content):
                    print(f"检测到引用消息格式模式: {pattern} 在消息中: {message.content[:50]}...")
                    return True
            
            return False
            
        except Exception as e:
            print(f"检测引用消息失败: {e}")
            return False
    
    # 测试用例
    test_cases = [
        ("@The end transformer 你好，请问Python怎么学习？", False, "正常@消息"),
        ("@The end transformer 引用的消息：Python很难学吗？", True, "包含引用关键词"),
        ("@The end transformer 回复了：我觉得可以试试", True, "包含回复关键词"),
        ("@The end transformer 「这是引用的内容」我想问一下", True, "包含微信引用符号"),
        ("@The end transformer 用户名: 原始消息内容\n我的问题是什么？", True, "包含引用格式"),
        ("@The end transformer >> 引用内容\n我想问个问题", True, "包含>符号"),
        ("@The end transformer 请帮我解释一下这个代码", False, "正常@消息（无引用）"),
    ]
    
    print("🧪 开始测试引用消息检测功能:")
    print("=" * 60)
    
    for i, (content, expected, description) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {description}")
        print(f"消息内容: {content}")
        
        message = MockMessage(content)
        result = contains_quoted_message(message)
        
        print(f"检测结果: {result}")
        print(f"期望结果: {expected}")
        
        if result == expected:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        print("-" * 40)
    
    print("\n🎉 引用消息检测功能测试完成")

if __name__ == "__main__":
    test_quote_detection()
