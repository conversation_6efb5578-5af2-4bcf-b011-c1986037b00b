# -*- coding: UTF-8 -*-
"""
数据存储模块
用于保存群聊消息数据到Excel文件
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from pathlib import Path
from loguru import logger

from .models import Message, ViolationRecord, MessageType


class DataStorage:
    """数据存储管理器"""
    
    def __init__(self, storage_path: str = "./monitor_data"):
        """
        初始化数据存储
        
        Args:
            storage_path: 数据存储路径
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 消息数据缓存
        self.message_cache = []
        self.violation_cache = []
        
        logger.info(f"数据存储初始化完成，存储路径: {self.storage_path}")
    
    def add_message(self, message: Message, is_violation: bool = False):
        """
        添加消息到缓存
        
        Args:
            message: 消息对象
            is_violation: 是否为违规消息
        """
        # 过滤系统消息和时间消息
        if message.message_type in [MessageType.SYSTEM, MessageType.TIME]:
            return
        
        message_data = {
            "发言时间": message.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "发言内容": message.content,
            "发言人": message.sender,
            "是否违规": "是" if is_violation else "否",
            "消息类型": message.message_type.value,
            "群聊名称": message.group_name,
            "消息ID": message.id
        }
        
        self.message_cache.append(message_data)
        logger.debug(f"添加消息到缓存: [{message.sender}] {message.content}")
    
    def add_violation_record(self, violation: ViolationRecord):
        """
        添加违规记录到缓存
        
        Args:
            violation: 违规记录对象
        """
        violation_data = violation.to_dict()
        self.violation_cache.append(violation_data)
        logger.warning(f"添加违规记录: [{violation.user_name}] {violation.violation_type}")
    
    def save_daily_messages(self, group_name: str, target_date: datetime = None) -> str:
        """
        保存指定日期的消息数据到Excel文件
        
        Args:
            group_name: 群聊名称
            target_date: 目标日期，默认为今天
            
        Returns:
            str: 保存的文件路径
        """
        if target_date is None:
            target_date = datetime.now()
        
        # 计算24小时周期：前一天24点到当天24点
        start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(days=1)
        
        # 过滤指定时间范围内的消息
        filtered_messages = []
        for msg_data in self.message_cache:
            msg_time = datetime.strptime(msg_data["发言时间"], "%Y-%m-%d %H:%M:%S")
            if start_time <= msg_time < end_time:
                filtered_messages.append(msg_data)
        
        if not filtered_messages:
            logger.warning(f"没有找到 {start_time.strftime('%Y-%m-%d')} 的消息数据")
            return ""
        
        # 创建DataFrame
        df = pd.DataFrame(filtered_messages)
        
        # 按时间排序
        df = df.sort_values("发言时间")
        
        # 生成文件名
        date_str = target_date.strftime("%Y%m%d")
        filename = f"{group_name}_消息记录_{date_str}.xlsx"
        filepath = self.storage_path / filename
        
        # 保存到Excel
        try:
            with pd.ExcelWriter(str(filepath), engine='openpyxl') as writer:
                # 消息详情表
                df.to_excel(writer, sheet_name='消息详情', index=False)
                
                # 统计汇总表
                stats_df = self._generate_statistics(filtered_messages, start_time, end_time)
                stats_df.to_excel(writer, sheet_name='统计汇总', index=False)
                
                # 违规记录表（如果有）
                violation_df = self._get_violations_in_timerange(start_time, end_time)
                if not violation_df.empty:
                    violation_df.to_excel(writer, sheet_name='违规记录', index=False)
            
            logger.info(f"消息数据已保存到: {filepath}")
            logger.info(f"共保存 {len(filtered_messages)} 条消息")
            
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存消息数据失败: {e}")
            return ""
    
    def _generate_statistics(self, messages: List[Dict], start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        生成统计汇总数据
        
        Args:
            messages: 消息列表
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            pd.DataFrame: 统计数据
        """
        stats_data = []
        
        # 基本统计信息
        stats_data.append({
            "统计项目": "统计时间范围",
            "统计值": f"{start_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_time.strftime('%Y-%m-%d %H:%M:%S')}"
        })
        
        stats_data.append({
            "统计项目": "总消息数",
            "统计值": len(messages)
        })
        
        # 按发言人统计
        user_counts = {}
        violation_counts = {}
        message_type_counts = {}
        
        for msg in messages:
            user = msg["发言人"]
            user_counts[user] = user_counts.get(user, 0) + 1
            
            if msg["是否违规"] == "是":
                violation_counts[user] = violation_counts.get(user, 0) + 1
            
            msg_type = msg["消息类型"]
            message_type_counts[msg_type] = message_type_counts.get(msg_type, 0) + 1
        
        stats_data.append({
            "统计项目": "活跃用户数",
            "统计值": len(user_counts)
        })
        
        stats_data.append({
            "统计项目": "违规消息数",
            "统计值": sum(1 for msg in messages if msg["是否违规"] == "是")
        })
        
        # 添加分隔行
        stats_data.append({"统计项目": "--- 用户发言排行 ---", "统计值": ""})
        
        # 用户发言排行（前10名）
        sorted_users = sorted(user_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (user, count) in enumerate(sorted_users[:10]):
            violation_count = violation_counts.get(user, 0)
            stats_data.append({
                "统计项目": f"第{i+1}名: {user}",
                "统计值": f"{count}条消息 (违规{violation_count}条)"
            })
        
        # 添加分隔行
        stats_data.append({"统计项目": "--- 消息类型分布 ---", "统计值": ""})
        
        # 消息类型分布
        for msg_type, count in message_type_counts.items():
            stats_data.append({
                "统计项目": f"{msg_type}消息",
                "统计值": f"{count}条"
            })
        
        return pd.DataFrame(stats_data)
    
    def _get_violations_in_timerange(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        获取指定时间范围内的违规记录
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            pd.DataFrame: 违规记录数据
        """
        filtered_violations = []
        
        for violation in self.violation_cache:
            violation_time = datetime.strptime(violation["检测时间"], "%Y-%m-%d %H:%M:%S")
            if start_time <= violation_time < end_time:
                filtered_violations.append(violation)
        
        return pd.DataFrame(filtered_violations)
    
    def save_realtime_data(self, group_name: str) -> str:
        """
        实时保存当前缓存的所有数据
        
        Args:
            group_name: 群聊名称
            
        Returns:
            str: 保存的文件路径
        """
        if not self.message_cache:
            logger.warning("没有消息数据可保存")
            return ""
        
        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{group_name}_实时数据_{timestamp}.xlsx"
        filepath = self.storage_path / filename
        
        try:
            df = pd.DataFrame(self.message_cache)
            df = df.sort_values("发言时间")
            
            with pd.ExcelWriter(str(filepath), engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='消息记录', index=False)
                
                if self.violation_cache:
                    violation_df = pd.DataFrame(self.violation_cache)
                    violation_df.to_excel(writer, sheet_name='违规记录', index=False)
            
            logger.info(f"实时数据已保存到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存实时数据失败: {e}")
            return ""
    
    def clear_cache(self):
        """清空缓存数据"""
        self.message_cache.clear()
        self.violation_cache.clear()
        logger.info("数据缓存已清空")
    
    def log_group_name_change(self, old_name: str, new_name: str, timestamp: datetime):
        """
        记录群名修改事件
        
        Args:
            old_name: 旧群名
            new_name: 新群名
            timestamp: 修改时间
        """
        try:
            # 创建群名修改记录
            change_record = {
                "事件时间": timestamp.strftime("%Y-%m-%d %H:%M:%S") if isinstance(timestamp, datetime) else str(timestamp),
                "事件类型": "群名修改",
                "旧群名": old_name or "未知",
                "新群名": new_name,
                "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 保存到专门的群名修改日志文件
            log_filename = f"群名修改记录_{datetime.now().strftime('%Y%m')}.xlsx"
            log_filepath = self.storage_path / log_filename
            
            # 读取现有记录（如果文件存在）
            existing_records = []
            if log_filepath.exists():
                try:
                    existing_df = pd.read_excel(str(log_filepath))
                    existing_records = existing_df.to_dict('records')
                except Exception as e:
                    logger.warning(f"读取现有群名修改记录失败: {e}")
            
            # 添加新记录
            existing_records.append(change_record)
            
            # 保存更新后的记录
            df = pd.DataFrame(existing_records)
            df.to_excel(str(log_filepath), index=False)
            
            logger.info(f"群名修改事件已记录到: {log_filepath}")
            logger.info(f"记录内容: {old_name} -> {new_name}")
            
        except Exception as e:
            logger.error(f"记录群名修改事件失败: {e}")
    
    def get_cache_stats(self) -> Dict:
        """
        获取缓存统计信息
        
        Returns:
            Dict: 缓存统计数据
        """
        return {
            "消息数量": len(self.message_cache),
            "违规记录数量": len(self.violation_cache),
            "存储路径": str(self.storage_path)
        }