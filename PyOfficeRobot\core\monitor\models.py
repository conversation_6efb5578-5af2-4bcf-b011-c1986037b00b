# -*- coding: UTF-8 -*-
"""
数据模型定义
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional


class MessageType(Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"
    TIME = "time"
    RECALL = "recall"


@dataclass
class Message:
    """消息数据模型"""
    id: str                           # 消息唯一标识
    sender: str                       # 发送者昵称
    content: str                      # 消息内容
    message_type: MessageType         # 消息类型
    timestamp: datetime               # 发送时间
    group_name: str                   # 群聊名称
    raw_data: tuple                   # 原始数据
    
    @classmethod
    def from_raw_data(cls, raw_data: tuple, group_name: str) -> 'Message':
        """从原始数据创建Message对象"""
        sender, content, msg_id = raw_data
        
        # 确定消息类型
        message_type = cls._determine_message_type(sender, content)
            
        return cls(
            id=msg_id,
            sender=sender,
            content=content,
            message_type=message_type,
            timestamp=datetime.now(),
            group_name=group_name,
            raw_data=raw_data
        )
    
    @staticmethod
    def _determine_message_type(sender: str, content: str) -> MessageType:
        """确定消息类型"""
        # 系统消息
        if sender == 'SYS':
            return MessageType.SYSTEM
        
        # 时间消息
        if sender == 'Time':
            return MessageType.TIME
        
        # 撤回消息
        if '撤回' in content or 'recalled' in content.lower():
            return MessageType.RECALL
        
        # 图片消息
        if content == '[图片]' or content == '[Image]':
            return MessageType.IMAGE
        
        # 文件消息（各种文件类型）
        file_patterns = [
            '[文件]', '[File]', '[视频]', '[Video]', '[语音]', '[Voice]',
            '[音乐]', '[Music]', '[位置]', '[Location]', '[链接]', '[Link]',
            '[小程序]', '[MiniProgram]', '[表情]', '[Emoji]', '[红包]', '[RedPacket]'
        ]
        
        for pattern in file_patterns:
            if pattern in content:
                return MessageType.FILE
        
        # 其他以[]包围的内容也视为文件
        if content.startswith('[') and content.endswith(']') and len(content) > 2:
            return MessageType.FILE
        
        # 默认为文本消息
        return MessageType.TEXT


@dataclass
class ViolationRecord:
    """违规记录数据模型"""
    user_id: str                      # 用户标识
    user_name: str                    # 用户昵称
    violation_type: str               # 违规类型
    violation_content: str            # 违规内容
    detection_time: datetime          # 检测时间
    group_name: str                   # 群聊名称
    confidence: float                 # 置信度
    message_id: str                   # 关联的消息ID
    
    def to_dict(self) -> dict:
        """转换为字典格式，用于Excel导出"""
        return {
            "用户ID": self.user_id,
            "用户昵称": self.user_name,
            "违规类型": self.violation_type,
            "违规内容": self.violation_content,
            "检测时间": self.detection_time.strftime("%Y-%m-%d %H:%M:%S"),
            "群聊名称": self.group_name,
            "置信度": self.confidence,
            "消息ID": self.message_id
        }