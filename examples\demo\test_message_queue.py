#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试群名修改期间的消息队列功能
验证暂停其他功能和消息队列处理机制
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')
sys.path.insert(0, '../..')

from PyOfficeRobot.core.monitor.group_monitor import GroupMonitor
from PyOfficeRobot.core.monitor.models import Message, MessageType, ViolationRecord

def test_message_queue_functionality():
    """测试消息队列功能的完整流程"""
    print("🧪 测试群名修改期间的消息队列功能")
    print("=" * 60)
    
    # 1. 创建监控器实例
    print("📋 步骤1: 创建监控器实例")
    candidate_groups = ["test_demo1", "test_demo2", "浪前 6.1-运营官交流群（全员）"]
    monitor = GroupMonitor(
        enable_ai_analysis=False,
        enable_data_storage=True,
        storage_path="./test_supervisor",
        my_username="The end",
        enable_qa=True,
        enable_violation_warning=True,
        candidate_group_names=candidate_groups
    )
    
    print(f"✅ 监控器初始化完成")
    print(f"📋 候选群聊列表: {monitor.candidate_group_names}")
    print(f"🎯 当前群聊: {monitor.current_group}")
    print(f"🔄 群名修改状态: {monitor.is_group_name_changing}")
    print(f"📤 消息队列大小: {len(monitor.message_queue)}")
    
    # 2. 设置初始状态
    print("\n📋 步骤2: 设置初始状态")
    monitor.current_group = "test_demo1"
    print(f"📝 设置当前群聊: {monitor.current_group}")
    
    # 3. 模拟正常消息处理
    print("\n📨 步骤3: 模拟正常消息处理")
    
    normal_messages = [
        Message(
            id="normal_msg_001",
            sender="用户A",
            content="这是一条正常消息",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户A", "这是一条正常消息", "normal_msg_001")
        ),
        Message(
            id="normal_msg_002",
            sender="用户B",
            content="@The end 你好，有问题想问",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户B", "@The end 你好，有问题想问", "normal_msg_002")
        ),
        Message(
            id="normal_msg_003",
            sender="用户C",
            content="转账给我",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户C", "转账给我", "normal_msg_003")
        )
    ]
    
    print(f"📝 处理 {len(normal_messages)} 条正常消息:")
    for i, message in enumerate(normal_messages, 1):
        print(f"  消息 {i}: [{message.sender}] {message.content}")
        # 模拟处理消息（不实际调用，只显示逻辑）
        print(f"    ✅ 正常处理模式")
    
    # 4. 模拟群名修改消息
    print("\n🔄 步骤4: 模拟群名修改消息")
    
    group_name_change_message = Message(
        id="system_msg_001",
        sender="SYS",
        content='"凡梦"修改群名为"test_demo2"',
        message_type=MessageType.SYSTEM,
        timestamp=datetime.now(),
        group_name="test_demo1",
        raw_data=("SYS", '"凡梦"修改群名为"test_demo2"', "system_msg_001")
    )
    
    print(f"📨 群名修改消息: {group_name_change_message.content}")
    print(f"🔄 开始群名修改过程...")
    
    # 模拟开始群名修改过程
    monitor._start_group_name_change_process()
    
    print(f"📝 群名修改状态:")
    print(f"    群名修改中: {monitor.is_group_name_changing}")
    print(f"    开始时间: {monitor.group_change_start_time}")
    print(f"    消息队列大小: {len(monitor.message_queue)}")
    
    # 5. 模拟群名修改期间的消息
    print("\n📤 步骤5: 模拟群名修改期间的消息")
    
    queued_messages = [
        Message(
            id="queued_msg_001",
            sender="用户D",
            content="这条消息应该被加入队列",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户D", "这条消息应该被加入队列", "queued_msg_001")
        ),
        Message(
            id="queued_msg_002",
            sender="用户E",
            content="@The end 这个问答也应该被队列",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户E", "@The end 这个问答也应该被队列", "queued_msg_002")
        ),
        Message(
            id="queued_msg_003",
            sender="用户F",
            content="加我微信",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("用户F", "加我微信", "queued_msg_003")
        ),
        Message(
            id="queued_msg_004",
            sender="SYS",
            content="以下为新消息",
            message_type=MessageType.SYSTEM,
            timestamp=datetime.now(),
            group_name="test_demo1",
            raw_data=("SYS", "以下为新消息", "queued_msg_004")
        )
    ]
    
    print(f"📝 模拟 {len(queued_messages)} 条消息在群名修改期间到达:")
    for i, message in enumerate(queued_messages, 1):
        print(f"  消息 {i}: [{message.sender}] {message.content}")
        
        # 模拟处理消息（会被加入队列）
        if monitor.is_group_name_changing:
            print(f"    🔄 群名修改中，消息加入队列")
            monitor.message_queue.append(message)
        else:
            print(f"    ✅ 正常处理模式")
    
    print(f"📊 队列状态:")
    print(f"    消息队列大小: {len(monitor.message_queue)}")
    print(f"    队列中的消息:")
    for i, msg in enumerate(monitor.message_queue, 1):
        print(f"      {i}. [{msg.sender}] {msg.content[:30]}...")
    
    # 6. 模拟群名修改完成
    print("\n✅ 步骤6: 模拟群名修改完成")
    
    # 更新当前群聊名称
    monitor.current_group = "test_demo2"
    print(f"📝 群聊名称已更新为: {monitor.current_group}")
    
    # 结束群名修改过程
    print(f"🔄 结束群名修改过程，处理队列消息...")
    monitor._end_group_name_change_process()
    
    print(f"📊 处理完成后的状态:")
    print(f"    群名修改中: {monitor.is_group_name_changing}")
    print(f"    消息队列大小: {len(monitor.message_queue)}")
    print(f"    当前群聊: {monitor.current_group}")
    
    # 7. 测试队列状态查询
    print("\n📊 步骤7: 测试队列状态查询")
    
    queue_status = monitor.get_queue_status()
    print(f"📝 队列状态信息:")
    for key, value in queue_status.items():
        print(f"    {key}: {value}")
    
    # 8. 模拟恢复后的正常消息处理
    print("\n📨 步骤8: 模拟恢复后的正常消息处理")
    
    post_change_messages = [
        Message(
            id="post_msg_001",
            sender="用户G",
            content="群名修改后的正常消息",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo2",
            raw_data=("用户G", "群名修改后的正常消息", "post_msg_001")
        ),
        Message(
            id="post_msg_002",
            sender="用户H",
            content="@The end 现在问答功能恢复了吗？",
            message_type=MessageType.TEXT,
            timestamp=datetime.now(),
            group_name="test_demo2",
            raw_data=("用户H", "@The end 现在问答功能恢复了吗？", "post_msg_002")
        )
    ]
    
    print(f"📝 处理 {len(post_change_messages)} 条恢复后的消息:")
    for i, message in enumerate(post_change_messages, 1):
        print(f"  消息 {i}: [{message.sender}] {message.content}")
        print(f"    ✅ 正常处理模式（功能已恢复）")
    
    # 9. 总结测试结果
    print("\n📊 测试总结:")
    print(f"   当前群聊: {monitor.current_group}")
    print(f"   群名修改状态: {monitor.is_group_name_changing}")
    print(f"   消息队列大小: {len(monitor.message_queue)}")
    print(f"   已处理消息数: {len(monitor.processed_message_ids)}")
    
    print("\n🎉 消息队列功能测试完成")
    print("=" * 60)
    
    return {
        "current_group": monitor.current_group,
        "is_group_name_changing": monitor.is_group_name_changing,
        "queue_size": len(monitor.message_queue),
        "processed_messages": len(monitor.processed_message_ids),
        "test_passed": True
    }

def demonstrate_queue_mechanism():
    """演示消息队列机制"""
    print("\n🔧 演示消息队列机制")
    print("=" * 40)
    
    print("🔄 群名修改过程中的消息处理流程:")
    print("   1. 检测到群名修改消息")
    print("   2. 设置 is_group_name_changing = True")
    print("   3. 暂停QA和违规检测功能")
    print("   4. 新消息加入 message_queue 而不处理")
    print("   5. 执行群名修改和重连逻辑")
    print("   6. 群名修改完成后设置 is_group_name_changing = False")
    print("   7. 处理队列中的所有消息")
    print("   8. 恢复正常的QA和违规检测功能")
    
    print("\n✅ 消息队列的优势:")
    print("   ✅ 避免群名修改期间的功能冲突")
    print("   ✅ 确保消息不丢失，延迟处理")
    print("   ✅ 群名修改完成后批量处理队列消息")
    print("   ✅ 保持系统状态的一致性")
    print("   ✅ 提供详细的队列状态监控")
    
    print("\n🛠️ 核心实现技术:")
    print("   ✅ 状态标志：is_group_name_changing")
    print("   ✅ 消息队列：message_queue[]")
    print("   ✅ 时间跟踪：group_change_start_time")
    print("   ✅ 队列处理：_process_queued_message()")
    print("   ✅ 状态管理：_start/end_group_name_change_process()")
    
    print("\n📊 处理的消息类型:")
    print("   📨 文本消息：加入队列，稍后处理")
    print("   🤖 @问答消息：加入队列，稍后处理")
    print("   🚨 违规消息：加入队列，稍后处理")
    print("   ⏰ 系统消息：过滤掉，不加入队列")
    print("   🔄 群名修改消息：立即处理，不加入队列")

if __name__ == "__main__":
    try:
        # 运行功能测试
        result = test_message_queue_functionality()
        
        # 演示队列机制
        demonstrate_queue_mechanism()
        
        print(f"\n🎯 测试结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()