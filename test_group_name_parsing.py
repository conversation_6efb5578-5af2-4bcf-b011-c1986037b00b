#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试群名解析逻辑
"""

import re

def test_group_name_parsing():
    """测试群名解析逻辑"""
    print("🧪 测试群名解析逻辑")
    print("=" * 50)
    
    # 测试消息
    test_message = '"凡梦"修改群名为"test_demo1"'
    print(f"📝 测试消息: {test_message}")
    
    # 当前的正则表达式模式
    patterns = [
        r'修改群名为"([^"]+)"',  # 标准格式：修改群名为"群名"
        r'修改群名为"([^"]*)"',  # 允许空群名
        r'"([^"]*)"修改群名为"([^"]*)"',  # 完整格式："用户"修改群名为"群名"
        r'群名为"([^"]+)"',      # 简化格式：群名为"群名"
    ]
    
    print(f"\n🔍 测试各个正则表达式模式:")
    
    for i, pattern in enumerate(patterns, 1):
        print(f"\n  模式 {i}: {pattern}")
        match = re.search(pattern, test_message)
        
        if match:
            print(f"    ✅ 匹配成功")
            print(f"    匹配组数: {len(match.groups())}")
            for j, group in enumerate(match.groups(), 1):
                print(f"    组 {j}: '{group}'")
            
            # 根据模式提取群名
            if pattern == r'"([^"]*)"修改群名为"([^"]*)"':
                # 对于完整格式，取第二个匹配组（群名）
                new_group_name = match.group(2).strip()
                print(f"    提取的群名 (组2): '{new_group_name}'")
            else:
                # 对于其他格式，取第一个匹配组
                new_group_name = match.group(1).strip()
                print(f"    提取的群名 (组1): '{new_group_name}'")
            
            # 清理群名
            if new_group_name:
                # 移除开头和结尾的引号
                cleaned_name = new_group_name.strip('"\'`""''「」【】')
                # 移除可能的转义字符
                cleaned_name = cleaned_name.replace('\\"', '').replace("\\'", '')
                # 清理空格
                cleaned_name = cleaned_name.strip()
                
                print(f"    清理后的群名: '{cleaned_name}'")
                
                if cleaned_name:
                    print(f"    🎉 最终结果: '{cleaned_name}'")
                    break
                else:
                    print(f"    ❌ 清理后群名为空")
            else:
                print(f"    ❌ 提取的群名为空")
        else:
            print(f"    ❌ 匹配失败")
    
    print(f"\n🔧 手动测试完整格式模式:")
    pattern = r'"([^"]*)"修改群名为"([^"]*)"'
    match = re.search(pattern, test_message)
    
    if match:
        print(f"✅ 完整格式匹配成功")
        print(f"完整匹配: '{match.group(0)}'")
        print(f"用户名 (组1): '{match.group(1)}'")
        print(f"群名 (组2): '{match.group(2)}'")
        
        # 提取群名
        group_name = match.group(2).strip()
        print(f"提取的群名: '{group_name}'")
        
        # 清理群名
        cleaned = group_name.strip('"\'`""''「」【】')
        print(f"清理后: '{cleaned}'")
        
        if cleaned:
            print(f"🎉 解析成功: '{cleaned}'")
        else:
            print(f"❌ 清理后为空")
    else:
        print(f"❌ 完整格式匹配失败")
    
    print(f"\n📊 测试其他消息格式:")
    
    test_messages = [
        '"凡梦"修改群名为"test_demo1"',
        '修改群名为"test_demo2"',
        '群名为"新群名"',
        '"用户"修改群名为"另一个群名"',
        '修改群名为"包含空格的群名"',
        '"张三"修改群名为""',  # 空群名
    ]
    
    for msg in test_messages:
        print(f"\n  测试消息: {msg}")
        
        for pattern in patterns:
            match = re.search(pattern, msg)
            if match:
                if pattern == r'"([^"]*)"修改群名为"([^"]*)"':
                    result = match.group(2).strip()
                else:
                    result = match.group(1).strip()
                
                cleaned = result.strip('"\'`""''「」【】').replace('\\"', '').replace("\\'", '').strip()
                
                if cleaned:
                    print(f"    ✅ 解析成功: '{cleaned}' (模式: {pattern})")
                    break
                else:
                    print(f"    ⚠️  解析为空: '{result}' -> '{cleaned}'")
        else:
            print(f"    ❌ 所有模式都匹配失败")

if __name__ == "__main__":
    test_group_name_parsing()