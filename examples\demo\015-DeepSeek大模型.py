# -*- coding: UTF-8 -*-
'''
@学习网站      ：https://www.python-office.com
@读者群     ：http://www.python4office.cn/wechat-group/
@作者  ：B站/抖音/微博/小红书/公众号，都叫：程序员晚枫，微信：python-office
@代码日期    ：2025/02/12 0:17
@本段代码的视频说明     ：
'''

# 导入PyOfficeRobot模块，免费下载：pip install PyOfficeRobot
import PyOfficeRobot

PyOfficeRobot.chat.chat_by_deepseek(who='晚枫', api_key="你自己的api_key")

"""
Python和PyCharm的安装：https://www.python-office.com/course-002/15-Python/15-Python.html

api_key免费获取：https://www.python-office.com/
"""
