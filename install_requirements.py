#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
PyOfficeRobot 依赖安装脚本
用于在新环境中安装所有必要的依赖包
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ 错误：需要Python 3.6或更高版本")
        print(f"当前版本：Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过：Python {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        if description:
            print(f"   说明：{description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败")
        print(f"错误信息：{e.stderr}")
        return False

def install_requirements():
    """安装requirements.txt中的依赖"""
    try:
        print("📦 正在安装requirements.txt中的所有依赖...")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True, check=True)
        
        print("✅ requirements.txt 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ requirements.txt 安装失败")
        print(f"错误信息：{e.stderr}")
        return False

def install_optional_packages():
    """安装可选的依赖包"""
    optional_packages = [
        ("zhipuai", "智谱AI SDK - 用于AI问答功能"),
        ("typing_extensions", "类型注解扩展 - Python < 3.8需要"),
    ]
    
    print("\n🔧 安装可选依赖包...")
    
    for package, description in optional_packages:
        choice = input(f"是否安装 {package}？({description}) [y/N]: ").strip().lower()
        if choice in ['y', 'yes']:
            install_package(package, description)

def check_installation():
    """检查关键包是否安装成功"""
    critical_packages = [
        "pandas",
        "loguru", 
        "schedule",
        "requests",
        "openpyxl"
    ]
    
    print("\n🔍 检查关键包安装状态...")
    
    all_installed = True
    for package in critical_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            all_installed = False
    
    # 检查Windows特定包
    if platform.system() == "Windows":
        windows_packages = ["pywin32", "pywinauto", "uiautomation"]
        print("\n🪟 检查Windows特定包...")
        
        for package in windows_packages:
            try:
                __import__(package)
                print(f"✅ {package} - 已安装")
            except ImportError:
                print(f"❌ {package} - 未安装")
                all_installed = False
    
    return all_installed

def main():
    """主函数"""
    print("🚀 PyOfficeRobot 依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查操作系统
    os_name = platform.system()
    print(f"🖥️  操作系统：{os_name}")
    
    if os_name != "Windows":
        print("⚠️  警告：此项目主要为Windows系统设计，其他系统可能无法正常使用所有功能")
    
    # 升级pip
    print("\n📦 升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip升级成功")
    except:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装requirements.txt
    print("\n📋 安装基础依赖...")
    if not install_requirements():
        print("❌ 基础依赖安装失败，请检查requirements.txt文件")
        return
    
    # 安装可选包
    install_optional_packages()
    
    # 检查安装结果
    print("\n" + "=" * 50)
    if check_installation():
        print("🎉 所有依赖安装完成！")
        print("\n📖 使用说明：")
        print("1. 确保微信客户端版本为3.9（下载链接见README.md）")
        print("2. 运行 python complete_supervisor.py 启动监督系统")
        print("3. 按照提示输入群聊名称和用户名")
    else:
        print("⚠️  部分依赖安装失败，请手动安装缺失的包")
        print("💡 提示：可以尝试使用以下命令手动安装：")
        print("   pip install -r requirements.txt")
        print("   pip install zhipuai")

if __name__ == "__main__":
    main()
