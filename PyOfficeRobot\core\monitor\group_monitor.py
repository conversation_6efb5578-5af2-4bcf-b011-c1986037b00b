# -*- coding: UTF-8 -*-
"""
群聊监控主控制器
"""
import time
from typing import List, Optional, Callable
from datetime import datetime
import re
import os
import hashlib
from loguru import logger
from ..WeChatType import WeChat
from .models import Message, MessageType, ViolationRecord
from .zhipu_analyzer import ZhipuAnalyzer, AnalysisResult
from .qa_handler import QAHandler

class GroupMonitor:
    """群聊监控主控制器"""
    def __init__(self, zhipu_api_key: str = None, enable_ai_analysis: bool = False, 
                 storage_path: str = "./monitor_data", enable_data_storage: bool = True,
                 my_username: str = None, enable_qa: bool = False, enable_violation_warning: bool = True,
                 candidate_group_names: list = None):
        """初始化监控器"""
        self.wx = WeChat()
        self.is_monitoring = False
        self.current_group = None
        self.last_processed_message_id = None
        self.message_handlers = []
        self.violation_records = []
        # 用户名设置（用于过滤自己的消息）
        self.my_username = my_username
        # 候选群聊名称列表
        self.candidate_group_names = candidate_group_names or [
            "test_demo1",
            "test_demo2",
            "浪前 6.1-运营官交流群（全员）",
            "富足社群-周周",
            "浪前 6.1",
            "浪前6.1-运营官",
            "浪前6.1-运营官交流群",
            "测试群",
            "工作群",
            "学习群"
        ]
        # 消息查重机制
        self.processed_message_ids = set()  # 已处理的消息ID集合
        self.recent_qa_questions = {}  # 最近的问答问题缓存 {question_hash: timestamp}
        
        # 群名修改处理机制
        self.is_group_name_changing = False  # 群名修改状态标志
        self.message_queue = []  # 群名修改期间的消息队列
        self.group_change_start_time = None  # 群名修改开始时间
        
        # 违规警告功能
        self.enable_violation_warning = enable_violation_warning
        self.violation_warning_template = "@{user} 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。"
        # 初始化智谱AI分析器
        self.enable_ai_analysis = enable_ai_analysis
        self.zhipu_analyzer = None
        if enable_ai_analysis:
            self.zhipu_analyzer = ZhipuAnalyzer(api_key=zhipu_api_key)
            logger.info("智谱AI分析功能已启用")
        # 初始化数据存储
        self.enable_data_storage = enable_data_storage
        self.data_storage = None
        if enable_data_storage:
            from .data_storage import DataStorage
            self.data_storage = DataStorage(storage_path)
            logger.info("数据存储功能已启用")
        # 初始化问答处理器
        self.enable_qa = enable_qa
        self.qa_handler = None
        if enable_qa and my_username:
            self.qa_handler = QAHandler(
                my_username=my_username,
                zhipu_api_key=zhipu_api_key,
                enable_auto_reply=True,
                candidate_group_names=self.candidate_group_names
            )
            self.qa_handler.set_wechat_client(self.wx)
            logger.info(f"问答功能已启用，监听@{my_username}的消息")
        # 违规警告功能状态
        if self.enable_violation_warning:
            logger.info("违规自动警告功能已启用")

    def add_message_handler(self, handler: Callable[[Message], None]):
        """添加消息处理器"""
        self.message_handlers.append(handler)

    def start_monitoring(self, group_name: str, check_interval: int = 3) -> bool:
        """
        启动群聊监控 - 支持智能群聊名称查找
        Args:
            group_name: 群聊名称（可以是候选名称之一）
            check_interval: 检查间隔（秒）
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info(f"开始监控群聊: {group_name}")
            # 智能查找真实的群聊名称
            actual_group_name = self._find_actual_group_name(group_name)
            if not actual_group_name:
                logger.error(f"无法找到匹配的群聊名称，候选名称: {self.candidate_group_names}")
                return False
            logger.info(f"找到实际群聊名称: {actual_group_name}")
            # 获取会话列表并打开群聊
            self.wx.GetSessionList()
            result = self.wx.ChatWith(actual_group_name)
            if not result:
                logger.error(f"无法打开群聊: {actual_group_name}")
                # 尝试其他候选名称
                for candidate in self.candidate_group_names:
                    if candidate != actual_group_name:
                        logger.info(f"尝试备选群聊名称: {candidate}")
                        result = self.wx.ChatWith(candidate)
                        if result:
                            actual_group_name = candidate
                            logger.info(f"成功使用备选群聊名称: {candidate}")
                            break
                if not result:
                    logger.error(f"所有候选群聊名称都无法打开")
                    return False
            self.current_group = actual_group_name
            self.is_monitoring = True
            # 开始监控循环
            self._monitoring_loop(check_interval)
            return True
        except Exception as e:
            logger.error(f"启动监控失败: {e}")
            return False

    def _find_actual_group_name(self, target_group: str) -> str:
        """
        查找真实的群聊名称 - 尝试所有候选群聊名称
        Args:
            target_group: 目标群聊名称
        Returns:
            str: 实际的群聊名称，如果找不到返回空字符串
        """
        try:
            logger.info(f"🔍 开始查找群聊，目标: {target_group}")
            logger.info(f"📋 候选群聊列表: {self.candidate_group_names}")
            # 获取会话列表
            session_list = self.wx.GetSessionList()
            if not session_list:
                logger.warning("无法获取会话列表")
                return ""
            logger.debug(f"获取到会话列表: {session_list}")
            # 遍历所有候选群聊名称进行查找
            for i, candidate in enumerate(self.candidate_group_names, 1):
                logger.info(f"🔍 尝试第{i}个候选群聊: {candidate}")
                # 策略1: 检查候选群聊是否直接在会话列表中
                for j, session in enumerate(session_list):
                    clean_session = self._clean_session_name(session)
                    if clean_session == candidate:
                        logger.info(f"✅ 在会话列表中找到群聊 (位置{j+1}): {candidate}")
                        return clean_session
                # 策略2: 使用搜索框搜索候选群聊名称
                logger.info(f"🔍 搜索候选群聊: {candidate}")
                try:
                    # 直接搜索候选群聊名称，不做任何修改
                    self.wx.Search(candidate)
                    time.sleep(2)  # 等待搜索结果
                    # 重新获取会话列表，看是否找到了目标群聊
                    updated_session_list = self.wx.GetSessionList()
                    if updated_session_list:
                        logger.debug(f"搜索 {candidate} 后的会话列表: {updated_session_list}")
                        # 检查搜索结果中是否有完全匹配的群聊
                        for session in updated_session_list:
                            clean_session = self._clean_session_name(session)
                            if clean_session == candidate:
                                logger.info(f"✅ 通过搜索找到群聊: {candidate}")
                                return clean_session
                    logger.warning(f"❌ 未找到候选群聊: {candidate}")
                except Exception as e:
                    logger.warning(f"搜索群聊 {candidate} 时出错: {e}")
                    continue
            # 如果所有候选群聊都没找到，返回空字符串
            logger.error(f"❌ 所有候选群聊都未找到: {self.candidate_group_names}")
            return ""
        except Exception as e:
            logger.error(f"查找群聊名称失败: {e}")
            return ""

    def _clean_session_name(self, session_name: str) -> str:
        """
        清理会话名称，移除消息计数等额外信息
        Args:
            session_name: 原始会话名称
        Returns:
            str: 清理后的会话名称
        """
        try:
            # 移除常见的消息计数模式
            # 移除 "数字条新消息" 模式
            cleaned = re.sub(r'\d+条新消息$', '', session_name).strip()
            # 移除 "(数字)" 模式
            cleaned = re.sub(r'\(\d+\)$', '', cleaned).strip()
            # 移除 "[数字]" 模式
            cleaned = re.sub(r'\[\d+\]$', '', cleaned).strip()
            # 不移除末尾的数字，因为它们可能是群聊名称的一部分
            # 例如：test_demo1, 群聊2 等
            # 如果清理后为空，返回原始名称
            if not cleaned:
                return session_name
            return cleaned
        except Exception as e:
            logger.debug(f"清理会话名称失败: {e}")
            return session_name

    def _normalize_group_name(self, group_name: str) -> str:
        """
        标准化群聊名称，用于模糊匹配
        Args:
            group_name: 群聊名称
        Returns:
            str: 标准化后的群聊名称
        """
        try:
            # 移除所有空格、标点符号，转为小写
            normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', group_name.lower())
            return normalized
        except Exception as e:
            logger.debug(f"标准化群聊名称失败: {e}")
            return group_name.lower()

    def _reactivate_chat_window(self):
        """重新激活聊天窗口 - 如果找不到群就在候选群列表中换一个群搜索"""
        try:
            logger.info("🔧 尝试重新激活聊天窗口...")
            # 先激活微信主窗口
            self._activate_wechat_main_window()
            # 重新获取会话列表
            logger.debug("重新获取会话列表...")
            session_list = self.wx.GetSessionList()
            logger.debug(f"获取到会话列表: {session_list}")
            # 策略1: 如果有当前群聊，尝试重新打开
            if self.current_group:
                logger.debug(f"尝试重新打开当前群聊: {self.current_group}")
                result = self.wx.ChatWith(self.current_group)
                if result:
                    logger.info("✅ 当前群聊重新激活成功")
                    return True
                else:
                    logger.warning(f"❌ 当前群聊重新激活失败: {self.current_group}")
            # 策略2: 在候选群聊名称中逐个尝试
            logger.info("🔍 在候选群聊列表中查找可用群聊...")
            for i, candidate in enumerate(self.candidate_group_names, 1):
                logger.info(f"🔍 尝试第{i}个候选群聊: {candidate}")
                # 首先检查是否在会话列表中
                found_in_session = False
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    if clean_session == candidate:
                        logger.info(f"✅ 在会话列表中找到候选群聊: {candidate}")
                        result = self.wx.ChatWith(candidate)
                        if result:
                            logger.info(f"✅ 成功激活候选群聊: {candidate}")
                            self.current_group = candidate
                            logger.info("🔧 聊天窗口重新激活完成")
                            return True
                        found_in_session = True
                        break
                # 如果不在会话列表中，尝试搜索
                if not found_in_session:
                    logger.info(f"🔍 搜索候选群聊: {candidate}")
                    try:
                        self.wx.Search(candidate)
                        time.sleep(2)  # 等待搜索结果
                        # 重新获取会话列表
                        updated_session_list = self.wx.GetSessionList()
                        if updated_session_list:
                            for session in updated_session_list:
                                clean_session = self._clean_session_name(session)
                                if clean_session == candidate:
                                    logger.info(f"✅ 通过搜索找到候选群聊: {candidate}")
                                    result = self.wx.ChatWith(candidate)
                                    if result:
                                        logger.info(f"✅ 成功激活搜索到的群聊: {candidate}")
                                        self.current_group = candidate
                                        logger.info("🔧 聊天窗口重新激活完成")
                                        return True
                                    break
                    except Exception as e:
                        logger.warning(f"搜索候选群聊 {candidate} 时出错: {e}")
                logger.warning(f"❌ 候选群聊 {candidate} 无法激活")
                time.sleep(0.5)  # 短暂等待后尝试下一个
            # 策略3: 如果所有候选群聊都无法激活，尝试使用会话列表中具有群聊特征的会话
            logger.info("🔍 尝试使用会话列表中具有群聊特征的会话...")
            if session_list:
                for session in session_list:
                    clean_session = self._clean_session_name(session)
                    # 检查是否看起来像群聊（包含群聊特征）
                    if self._looks_like_group_chat(clean_session):
                        logger.info(f"🔍 尝试使用具有群聊特征的会话: {clean_session}")
                        result = self.wx.ChatWith(clean_session)
                        if result:
                            logger.info(f"✅ 成功激活具有群聊特征的会话: {clean_session}")
                            self.current_group = clean_session
                            # 将这个群聊添加到候选列表开头
                            if clean_session not in self.candidate_group_names:
                                self.candidate_group_names.insert(0, clean_session)
                                logger.info(f"✅ 新群聊已添加到候选列表: {clean_session}")
                            logger.info("🔧 聊天窗口重新激活完成")
                            return True
                        time.sleep(0.5)
            logger.error("❌ 所有重新激活尝试都失败了")
            logger.error(f"💡 候选群聊列表: {self.candidate_group_names}")
            logger.error(f"💡 当前会话列表: {session_list}")
            return False
        except Exception as e:
            logger.error(f"重新激活聊天窗口出错: {e}")
            return False

    def _activate_wechat_main_window(self):
        """激活微信主窗口"""
        try:
            import win32gui
            import win32con
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    if "微信" in window_text or "WeChat" in window_text:
                        windows.append((hwnd, window_text))
                return True
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            # 找到微信主窗口并激活
            for hwnd, window_text in windows:
                if "微信" in window_text and len(window_text) <= 10:  # 主窗口标题通常较短
                    logger.debug(f"找到微信主窗口: {window_text}")
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(1)
                    logger.debug("✅ 微信主窗口已激活")
                    return True
            logger.warning("❌ 未找到微信主窗口")
            return False
        except Exception as e:
            logger.debug(f"激活微信主窗口失败: {e}")
            return False

    def _looks_like_group_chat(self, session_name: str) -> bool:
        """判断会话名称是否看起来像群聊"""
        try:
            # 群聊特征关键词
            group_keywords = ["群", "group", "团队", "交流", "讨论", "工作", "项目", "学习", "班级", "demo", "test"]
            # 检查是否包含群聊关键词
            session_lower = session_name.lower()
            for keyword in group_keywords:
                if keyword in session_lower:
                    return True
            # 检查是否包含数字和字母的组合（如test_demo1）
            if (any(char.isdigit() for char in session_name) and 
                any(char.isalpha() for char in session_name) and
                len(session_name) >= 5):
                return True
            # 检查是否包含特殊符号（群聊名称常有的符号）
            if any(char in "-_()（）[]【】" for char in session_name):
                return True
            return False
        except Exception as e:
            logger.debug(f"判断群聊特征失败: {e}")
            return False

    def _is_similar_group_name(self, candidate: str, session: str) -> bool:
        """
        检查两个群聊名称是否相似（处理 test_demo1 vs test_demo2 的情况）
        Args:
            candidate: 候选群聊名称
            session: 会话列表中的群聊名称
        Returns:
            bool: 是否相似
        """
        try:
            # 转换为小写进行比较
            candidate_lower = candidate.lower()
            session_lower = session.lower()
            # 如果长度差异太大，不认为相似
            if abs(len(candidate_lower) - len(session_lower)) > 3:
                return False
            # 计算相似度
            similarity = self._calculate_similarity(candidate_lower, session_lower)
            # 如果相似度超过80%，认为是相似的
            if similarity > 0.8:
                logger.debug(f"群聊名称相似度: {candidate} vs {session} = {similarity:.2f}")
                return True
            # 特殊处理：检查是否只是末尾数字不同（如 test_demo1 vs test_demo2）
            # 移除末尾的数字
            candidate_base = re.sub(r'\d+$', '', candidate_lower)
            session_base = re.sub(r'\d+$', '', session_lower)
            # 如果去掉数字后完全相同，认为是相似的
            if candidate_base == session_base and candidate_base:
                logger.debug(f"群聊名称基础部分相同: {candidate_base}")
                return True
            # 检查是否只是中间数字不同（如 test_demo1 vs test_demo2）
            candidate_pattern = re.sub(r'\d+', r'\\d+', candidate_lower)
            if re.match(candidate_pattern, session_lower):
                logger.debug(f"群聊名称模式匹配: {candidate_pattern} matches {session_lower}")
                return True
            return False
        except Exception as e:
            logger.debug(f"计算群聊名称相似度失败: {e}")
            return False

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度（使用编辑距离）
        Args:
            str1: 字符串1
            str2: 字符串2
        Returns:
            float: 相似度（0-1之间）
        """
        try:
            # 使用编辑距离算法计算相似度
            def levenshtein_distance(s1, s2):
                if len(s1) < len(s2):
                    return levenshtein_distance(s2, s1)
                if len(s2) == 0:
                    return len(s1)
                previous_row = list(range(len(s2) + 1))
                for i, c1 in enumerate(s1):
                    current_row = [i + 1]
                    for j, c2 in enumerate(s2):
                        insertions = previous_row[j + 1] + 1
                        deletions = current_row[j] + 1
                        substitutions = previous_row[j] + (c1 != c2)
                        current_row.append(min(insertions, deletions, substitutions))
                    previous_row = current_row
                return previous_row[-1]
            max_len = max(len(str1), len(str2))
            if max_len == 0:
                return 1.0
            distance = levenshtein_distance(str1, str2)
            similarity = 1 - (distance / max_len)
            return similarity
        except Exception as e:
            logger.debug(f"计算字符串相似度失败: {e}")
            return 0.0

    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.current_group = None
        logger.info("监控已停止")

    def get_monitoring_status(self) -> dict:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "current_group": self.current_group,
            "violation_count": len(self.violation_records),
            "last_check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def _monitoring_loop(self, check_interval: int):
        """监控主循环"""
        logger.info(f"开始监控循环，检查间隔: {check_interval}秒")
        while self.is_monitoring:
            try:
                # 获取最新消息
                new_messages = self._get_new_messages()
                # 处理新消息
                for message in new_messages:
                    self._process_message(message)
                time.sleep(check_interval)
            except KeyboardInterrupt:
                logger.info("收到停止信号，正在停止监控...")
                self.stop_monitoring()
                break
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(check_interval)

    def _get_new_messages(self) -> List[Message]:
        """获取新消息 - 使用最后处理的消息ID来过滤"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 获取所有消息
                all_messages = self.wx.GetAllMessage
                if not all_messages:
                    if attempt < max_retries - 1:
                        logger.warning(f"获取消息为空，尝试重新激活窗口 (尝试 {attempt + 1}/{max_retries})")
                        self._reactivate_chat_window()
                        time.sleep(2)
                        continue
                    else:
                        logger.warning("多次尝试后仍无法获取消息")
                        return []
                new_messages = []
                # 如果是第一次运行，记录最后一条消息的ID，不处理任何历史消息
                if self.last_processed_message_id is None:
                    if all_messages:
                        # 获取最后一条消息的ID作为起始点
                        try:
                            last_raw_msg = all_messages[-1]
                            last_message = Message.from_raw_data(last_raw_msg, self.current_group)
                            self.last_processed_message_id = last_message.id
                            logger.info(f"监控初始化完成，从消息ID {self.last_processed_message_id} 开始监控，跳过 {len(all_messages)} 条历史消息")
                        except Exception as e:
                            logger.warning(f"初始化失败: {e}")
                            self.last_processed_message_id = "initialized"
                    else:
                        self.last_processed_message_id = "initialized"
                    return []  # 第一次运行不返回任何消息
                # 找到上次处理的消息位置
                last_processed_index = -1
                for i, raw_msg in enumerate(all_messages):
                    try:
                        message = Message.from_raw_data(raw_msg, self.current_group)
                        if message.id == self.last_processed_message_id:
                            last_processed_index = i
                            break
                    except:
                        continue
                # 如果找到了上次处理的消息，只处理之后的消息
                if last_processed_index >= 0:
                    new_raw_messages = all_messages[last_processed_index + 1:]
                else:
                    # 如果没找到上次处理的消息（可能消息列表已更新），只处理最新的1条
                    # 避免重复处理大量历史消息
                    new_raw_messages = all_messages[-1:] if all_messages else []
                    logger.warning(f"未找到上次处理的消息ID {self.last_processed_message_id}，只处理最新1条消息")
                # 处理新消息
                for raw_msg in new_raw_messages:
                    try:
                        message = Message.from_raw_data(raw_msg, self.current_group)
                        # 跳过已处理的消息（双重保险）
                        if message.id in self.processed_message_ids:
                            logger.debug(f"跳过已处理消息: [{message.sender}] {message.content[:30]}...")
                            continue
                        # 跳过自己发送的消息（在这里也进行过滤）
                        if self.my_username and message.sender == self.my_username:
                            logger.debug(f"跳过自己的消息（获取阶段）: [{message.sender}] {message.content[:30]}...")
                            self.processed_message_ids.add(message.id)  # 添加到已处理集合
                            continue
                        # 跳过包含自己回复内容的消息（额外保护）
                        if self.my_username and "@" in message.content and "决策树" in message.content and len(message.content) > 50:
                            logger.debug(f"跳过疑似自己的回复消息: [{message.sender}] {message.content[:50]}...")
                            self.processed_message_ids.add(message.id)
                            continue
                        new_messages.append(message)
                        logger.debug(f"新消息: [{message.sender}] {message.content[:30]}...")
                    except Exception as e:
                        logger.warning(f"解析消息失败，跳过: {raw_msg} - {e}")
                        continue
                # 更新最后处理的消息ID
                if new_messages:
                    self.last_processed_message_id = new_messages[-1].id
                    logger.debug(f"处理了 {len(new_messages)} 条新消息")
                return new_messages
            except Exception as e:
                logger.error(f"获取新消息失败: {e}")
                if attempt < max_retries - 1:
                    logger.warning(f"尝试重新激活窗口后重试 (尝试 {attempt + 1}/{max_retries})")
                    self._reactivate_chat_window()
                    time.sleep(2)
                    continue
                else:
                    return []

    def _process_message(self, message: Message):
        """处理单条消息 - 支持群名修改期间的消息队列"""
        try:
            # 检测群名修改消息并处理
            if message.message_type == MessageType.SYSTEM and "修改群名为" in message.content:
                logger.info(f"检测到群名修改消息: {message.content}")
                self._handle_group_name_change(message)
                return
            
            # 如果正在进行群名修改，将消息加入队列而不处理
            if self.is_group_name_changing:
                logger.info(f"🔄 群名修改中，消息加入队列: [{message.sender}] {message.content[:30]}...")
                self.message_queue.append(message)
                return
            
            # 过滤系统消息和时间消息
            if message.message_type in [MessageType.SYSTEM, MessageType.TIME]:
                return
            # 过滤自己的消息，避免处理自己发送的消息
            if self.my_username and message.sender == self.my_username:
                logger.debug(f"跳过自己的消息: [{message.sender}] {message.content}")
                # 仍然需要添加到已处理消息集合，避免重复处理
                self.processed_message_ids.add(message.id)
                return
            # 消息查重，避免重复处理
            if message.id in self.processed_message_ids:
                logger.debug(f"跳过重复消息: [{message.sender}] {message.content}")
                return
            # 添加到已处理消息集合
            self.processed_message_ids.add(message.id)
            logger.info(f"处理消息: [{message.sender}] {message.content}")
            is_violation = False
            # 如果启用了问答功能，优先处理@消息
            if self.enable_qa and self.qa_handler:
                if self.qa_handler.handle_question(message):
                    # 问答查重机制
                    question_content = self.qa_handler.extract_question(message)
                    question_hash = hash(question_content.lower().strip())
                    current_time = datetime.now()
                    # 检查消息时间戳，只处理最近的消息（避免处理历史消息）
                    try:
                        if isinstance(message.timestamp, (int, float)):
                            message_time = datetime.fromtimestamp(message.timestamp)
                        elif isinstance(message.timestamp, datetime):
                            message_time = message.timestamp
                        else:
                            # 如果时间戳格式不正确，跳过时间检查
                            message_time = current_time
                        time_diff = (current_time - message_time).total_seconds()
                    except Exception as e:
                        logger.debug(f"时间戳处理失败，跳过时间检查: {e}")
                        time_diff = 0  # 设为0，不跳过消息
                    # 如果消息超过10分钟，认为是历史消息，跳过处理
                    if time_diff > 600:  # 10分钟
                        logger.info(f"跳过历史问答消息 (时间差: {time_diff:.1f}秒): [{message.sender}] {question_content}")
                        return
                    # 检查是否是重复问题（5分钟内）
                    if question_hash in self.recent_qa_questions:
                        last_time = self.recent_qa_questions[question_hash]
                        if (current_time - last_time).total_seconds() < 300:  # 5分钟内
                            logger.info(f"跳过重复问答: [{message.sender}] {question_content}")
                            return
                    # 记录问答时间
                    self.recent_qa_questions[question_hash] = current_time
                    logger.info(f"已处理问答消息: [{message.sender}] {message.content}")
            # 如果启用了AI分析，进行违规检测
            if self.enable_ai_analysis and self.zhipu_analyzer:
                is_violation = self._analyze_message_for_violations(message)
            # 如果启用了数据存储，保存消息
            if self.enable_data_storage and self.data_storage:
                self.data_storage.add_message(message, is_violation)
            # 调用所有消息处理器
            for handler in self.message_handlers:
                try:
                    handler(message)
                except Exception as e:
                    logger.error(f"消息处理器执行失败: {e}")
        except Exception as e:
            logger.error(f"处理消息失败: {e}")

    def _handle_group_name_change(self, message: Message):
        """
        处理群名修改的系统消息 - 简化版，只检测关键词
        Args:
            message: 包含群名修改信息的系统消息
        """
        try:
            logger.info(f"🔄 检测到群名修改关键词，开始群名更换操作: {message.content}")
            
            # 记录旧群名
            old_group_name = self.current_group
            logger.info(f"📝 当前群聊: '{old_group_name}'，准备进行群名更换操作")
            
            # 设置群名修改状态，暂停其他功能
            self._start_group_name_change_process()
            
            # 尝试重新连接（使用现有候选群聊列表）
            success = self._reconnect_to_available_group()
            
            if success:
                logger.info(f"🎉 群名修改处理成功，已重新连接到: '{self.current_group}'")
                
                # 记录群名修改事件到数据存储
                if self.enable_data_storage and self.data_storage:
                    try:
                        self.data_storage.log_group_name_change(old_group_name, self.current_group, message.timestamp)
                        logger.info(f"✅ 群名修改事件已记录到数据存储")
                    except Exception as e:
                        logger.warning(f"记录群名修改事件失败: {e}")
                
                # 群名修改完成，不需要发送验证消息
                logger.info(f"✅ 群名修改处理完成，无需发送验证消息")
                    
            else:
                logger.error(f"❌ 群名修改处理失败，无法重新连接到任何群聊")
                # 尝试回退到旧群名
                logger.info(f"🔄 尝试回退到旧群名: '{old_group_name}'")
                fallback_success = self._reconnect_to_new_group(old_group_name)
                if fallback_success:
                    logger.info(f"✅ 成功回退到旧群名: '{old_group_name}'")
                    self.current_group = old_group_name
                else:
                    logger.error(f"❌ 回退到旧群名也失败，监控可能中断")
            
            # 结束群名修改过程，恢复其他功能
            self._end_group_name_change_process()
            
        except Exception as e:
            logger.error(f"处理群名修改失败: {e}")
            # 确保即使出错也要结束群名修改过程
            self._end_group_name_change_process()
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")

    def _reconnect_to_available_group(self) -> bool:
        """
        重新连接到可用的群聊 - 不需要指定群名，从候选列表中查找
        Returns:
            bool: 重新连接是否成功
        """
        try:
            logger.info(f"🔄 开始重新连接到可用群聊")
            # 等待一下，让微信界面更新
            time.sleep(2)
            
            # 方法1: 在现有候选群聊中查找可用的群聊
            logger.info(f"🔍 在候选群聊列表中查找可用群聊")
            for i, candidate in enumerate(self.candidate_group_names, 1):
                logger.debug(f"尝试候选群聊 {i}: {candidate}")
                result = self.wx.ChatWith(candidate)
                if result and self._verify_group_connection(candidate):
                    logger.info(f"✅ 成功连接到候选群聊: '{candidate}'")
                    self.current_group = candidate
                    return True
                time.sleep(0.5)  # 短暂等待
            
            # 方法2: 使用通用的重新激活窗口方法
            logger.info(f"🔍 使用通用重新激活方法")
            if self._reactivate_chat_window():
                # 验证重新激活后的连接
                if self.current_group and self._verify_group_connection(self.current_group):
                    logger.info(f"✅ 重新激活后连接到: '{self.current_group}'")
                    return True
            
            logger.error(f"❌ 所有重新连接方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"重新连接到可用群聊失败: {e}")
            return False

    def _start_group_name_change_process(self):
        """
        开始群名修改过程，暂停其他功能
        """
        try:
            logger.info("🔄 开始群名修改过程，暂停QA和违规检测功能")
            self.is_group_name_changing = True
            self.group_change_start_time = datetime.now()
            self.message_queue.clear()  # 清空消息队列
            logger.info(f"✅ 群名修改状态已设置，开始时间: {self.group_change_start_time}")
        except Exception as e:
            logger.error(f"开始群名修改过程失败: {e}")

    def _end_group_name_change_process(self):
        """
        结束群名修改过程，恢复其他功能并处理队列中的消息
        """
        try:
            logger.info("🔄 结束群名修改过程，恢复QA和违规检测功能")
            
            # 计算群名修改耗时
            if self.group_change_start_time:
                duration = datetime.now() - self.group_change_start_time
                logger.info(f"📊 群名修改过程耗时: {duration.total_seconds():.2f}秒")
            
            # 处理队列中的消息
            queued_message_count = len(self.message_queue)
            if queued_message_count > 0:
                logger.info(f"📤 开始处理队列中的 {queued_message_count} 条消息")
                
                # 处理队列中的每条消息
                processed_count = 0
                for queued_message in self.message_queue.copy():  # 使用副本避免修改原列表
                    try:
                        # 重新处理消息（不包括群名修改消息）
                        if not (queued_message.message_type == MessageType.SYSTEM and "修改群名为" in queued_message.content):
                            logger.debug(f"处理队列消息: [{queued_message.sender}] {queued_message.content[:30]}...")
                            self._process_queued_message(queued_message)
                            processed_count += 1
                    except Exception as e:
                        logger.warning(f"处理队列消息失败: {e}")
                        continue
                
                logger.info(f"✅ 队列消息处理完成，成功处理 {processed_count}/{queued_message_count} 条消息")
            else:
                logger.info("📤 消息队列为空，无需处理")
            
            # 清空消息队列并重置状态
            self.message_queue.clear()
            self.is_group_name_changing = False
            self.group_change_start_time = None
            
            logger.info("✅ 群名修改过程结束，所有功能已恢复正常")
            
        except Exception as e:
            logger.error(f"结束群名修改过程失败: {e}")
            # 强制重置状态
            self.is_group_name_changing = False
            self.group_change_start_time = None
            self.message_queue.clear()

    def _process_queued_message(self, message: Message):
        """
        处理队列中的消息（不包括群名修改检测）
        Args:
            message: 队列中的消息
        """
        try:
            # 过滤系统消息和时间消息
            if message.message_type in [MessageType.SYSTEM, MessageType.TIME]:
                return
            
            # 过滤自己的消息
            if self.my_username and message.sender == self.my_username:
                logger.debug(f"跳过自己的消息（队列处理）: [{message.sender}] {message.content}")
                self.processed_message_ids.add(message.id)
                return
            
            # 消息查重
            if message.id in self.processed_message_ids:
                logger.debug(f"跳过重复消息（队列处理）: [{message.sender}] {message.content}")
                return
            
            # 添加到已处理消息集合
            self.processed_message_ids.add(message.id)
            logger.info(f"处理队列消息: [{message.sender}] {message.content}")
            
            is_violation = False
            
            # 处理问答功能
            if self.enable_qa and self.qa_handler:
                if self.qa_handler.handle_question(message):
                    logger.info(f"已处理队列中的问答消息: [{message.sender}] {message.content}")
            
            # 处理违规检测
            if self.enable_ai_analysis and self.zhipu_analyzer:
                is_violation = self._analyze_message_for_violations(message)
            
            # 数据存储
            if self.enable_data_storage and self.data_storage:
                self.data_storage.add_message(message, is_violation)
            
            # 调用消息处理器
            for handler in self.message_handlers:
                try:
                    handler(message)
                except Exception as e:
                    logger.error(f"队列消息处理器执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"处理队列消息失败: {e}")

    def get_queue_status(self) -> dict:
        """
        获取消息队列状态
        Returns:
            dict: 队列状态信息
        """
        return {
            "is_group_name_changing": self.is_group_name_changing,
            "queue_size": len(self.message_queue),
            "change_start_time": self.group_change_start_time.strftime("%Y-%m-%d %H:%M:%S") if self.group_change_start_time else None,
            "change_duration": (datetime.now() - self.group_change_start_time).total_seconds() if self.group_change_start_time else 0
        }


    def _update_candidate_groups_after_name_change(self, old_name: str, new_name: str):
        """
        群名修改后更新候选群聊列表 - 不添加新元素，只使用现有候选群聊
        Args:
            old_name: 旧群名
            new_name: 新群名
        """
        try:
            logger.info(f"🔄 群名变更记录: '{old_name}' -> '{new_name}'")
            logger.info(f"📋 当前候选群聊列表: {self.candidate_group_names}")
            
            # 检查新群名是否在现有候选列表中
            if new_name in self.candidate_group_names:
                logger.info(f"✅ 新群名在候选列表中: '{new_name}'")
            else:
                logger.info(f"ℹ️  新群名不在候选列表中，将使用现有候选群聊进行重连")
                
        except Exception as e:
            logger.error(f"更新候选群聊列表失败: {e}")

    def _reconnect_to_new_group(self, new_group_name: str) -> bool:
        """
        重新连接到新群聊 - 优化版，使用现有候选群聊列表
        Args:
            new_group_name: 新群聊名称
        Returns:
            bool: 重新连接是否成功
        """
        try:
            logger.info(f"🔄 尝试重新连接，目标群聊: '{new_group_name}'")
            # 等待一下，让微信界面更新
            time.sleep(2)
            
            # 方法1: 直接尝试打开新群聊
            logger.info(f"🔍 方法1: 直接尝试打开新群聊")
            result = self.wx.ChatWith(new_group_name)
            if result and self._verify_group_connection(new_group_name):
                logger.info(f"✅ 方法1成功: 直接打开新群聊 '{new_group_name}'")
                self.current_group = new_group_name
                return True
            
            # 方法2: 在现有候选群聊中查找匹配的群聊
            logger.info(f"🔍 方法2: 在候选群聊列表中查找匹配群聊")
            for candidate in self.candidate_group_names:
                logger.debug(f"尝试候选群聊: {candidate}")
                result = self.wx.ChatWith(candidate)
                if result and self._verify_group_connection(candidate):
                    logger.info(f"✅ 方法2成功: 使用候选群聊 '{candidate}'")
                    self.current_group = candidate
                    return True
                time.sleep(0.5)  # 短暂等待
            
            # 方法3: 使用通用的重新激活窗口方法
            logger.info(f"🔍 方法3: 使用通用重新激活方法")
            if self._reactivate_chat_window():
                # 验证重新激活后的连接
                if self.current_group and self._verify_group_connection(self.current_group):
                    logger.info(f"✅ 方法3成功: 重新激活后连接到 '{self.current_group}'")
                    return True
            
            logger.error(f"❌ 所有重新连接方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"重新连接到新群聊失败: {e}")
            return False

    def _verify_group_connection(self, group_name: str) -> bool:
        """
        验证群聊连接是否成功
        Args:
            group_name: 群聊名称
        Returns:
            bool: 连接是否成功
        """
        try:
            # 等待一下让界面稳定
            time.sleep(1)
            
            # 尝试获取消息来验证连接
            try:
                messages = self.wx.GetAllMessage
                if messages:
                    logger.debug(f"✅ 成功获取到 {len(messages)} 条消息，连接验证成功")
                    return True
                else:
                    logger.debug(f"❌ 未获取到消息，连接可能失败")
                    return False
            except Exception as e:
                logger.debug(f"❌ 获取消息时出错，连接验证失败: {e}")
                return False
                
        except Exception as e:
            logger.debug(f"验证群聊连接时出错: {e}")
            return False

    def _analyze_message_for_violations(self, message: Message):
        """分析消息是否违规"""
        try:
            analysis_result = None
            # 分析文本消息
            if message.message_type == MessageType.TEXT:
                analysis_result = self.zhipu_analyzer.analyze_text_content(message.content)
            # 分析图片消息（暂时跳过，因为需要图片路径）
            elif message.message_type == MessageType.IMAGE:
                logger.info(f"检测到图片消息，暂时跳过AI分析: [{message.sender}]")
                return False
            # 如果检测到违规，创建违规记录
            if analysis_result and analysis_result.is_violation:
                violation_record = ViolationRecord(
                    user_id=message.sender,  # 使用发送者作为用户ID
                    user_name=message.sender,
                    violation_type=analysis_result.violation_type,
                    violation_content=message.content,
                    detection_time=datetime.now(),
                    group_name=message.group_name,
                    confidence=analysis_result.confidence,
                    message_id=message.id
                )
                self.add_violation_record(violation_record)
                return True
            return False
        except Exception as e:
            logger.error(f"违规分析失败: {e}")
            return False

    def add_violation_record(self, record: ViolationRecord):
        """添加违规记录"""
        self.violation_records.append(record)
        logger.warning(f"检测到违规行为: [{record.user_name}] {record.violation_type}")
        # 如果启用了数据存储，也保存到存储中
        if self.enable_data_storage and self.data_storage:
            self.data_storage.add_violation_record(record)
        # 如果启用了违规警告，发送警告消息
        if self.enable_violation_warning:
            self._send_violation_warning(record)

    def get_violation_records(self) -> List[ViolationRecord]:
        """获取所有违规记录"""
        return self.violation_records.copy()

    def clear_violation_records(self):
        """清空违规记录"""
        self.violation_records.clear()
        logger.info("违规记录已清空")

    def save_daily_data(self, target_date: datetime = None) -> str:
        """
        保存指定日期的24小时数据到Excel文件
        Args:
            target_date: 目标日期，默认为今天
        Returns:
            str: 保存的文件路径
        """
        if not self.enable_data_storage or not self.data_storage:
            logger.error("数据存储功能未启用")
            return ""
        if not self.current_group:
            logger.error("当前没有监控的群聊")
            return ""
        return self.data_storage.save_daily_messages(self.current_group, target_date)

    def save_realtime_data(self) -> str:
        """
        保存当前实时数据到Excel文件
        Returns:
            str: 保存的文件路径
        """
        if not self.enable_data_storage or not self.data_storage:
            logger.error("数据存储功能未启用")
            return ""
        if not self.current_group:
            logger.error("当前没有监控的群聊")
            return ""
        return self.data_storage.save_realtime_data(self.current_group)

    def get_data_stats(self) -> dict:
        """
        获取数据统计信息
        Returns:
            dict: 统计信息
        """
        if not self.enable_data_storage or not self.data_storage:
            return {"error": "数据存储功能未启用"}
        return self.data_storage.get_cache_stats()

    def clear_data_cache(self):
        """清空数据缓存"""
        if self.enable_data_storage and self.data_storage:
            self.data_storage.clear_cache()
            logger.info("数据缓存已清空")

    def get_qa_history(self) -> List[dict]:
        """
        获取问答历史记录
        Returns:
            List[dict]: 问答历史
        """
        if not self.enable_qa or not self.qa_handler:
            return []
        return self.qa_handler.get_qa_history()

    def clear_qa_history(self):
        """清空问答历史"""
        if self.enable_qa and self.qa_handler:
            self.qa_handler.clear_qa_history()

    def add_preset_qa(self, keyword: str, answer: str):
        """
        添加预设问答
        Args:
            keyword: 关键词
            answer: 回答
        """
        if self.enable_qa and self.qa_handler:
            self.qa_handler.add_preset_qa(keyword, answer)

    def set_qa_auto_reply(self, enabled: bool):
        """
        设置问答自动回复开关
        Args:
            enabled: 是否启用自动回复
        """
        if self.enable_qa and self.qa_handler:
            self.qa_handler.set_auto_reply(enabled)

    def set_qa_reply_delay(self, delay: int):
        """
        设置问答回复延迟
        Args:
            delay: 延迟秒数
        """
        if self.enable_qa and self.qa_handler:
            self.qa_handler.set_reply_delay(delay)

    def _send_violation_warning(self, record: ViolationRecord):
        """
        发送违规警告消息
        Args:
            record: 违规记录
        """
        try:
            # 生成警告消息
            warning_message = self.violation_warning_template.format(user=record.user_name)
            # 添加发送延迟，避免发送过快
            time.sleep(1)
            # 尝试发送警告消息到群聊
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 使用改进的消息发送方法
                    success = self._send_message_to_current_chat(warning_message)
                    if success:
                        logger.info(f"已发送违规警告: {warning_message}")
                        return
                    else:
                        logger.warning(f"发送违规警告失败 (尝试 {attempt + 1}/{max_retries}): {warning_message}")
                        if attempt < max_retries - 1:
                            # 尝试重新激活群聊窗口
                            self._reactivate_chat_window()
                            time.sleep(2)  # 等待2秒后重试
                except Exception as send_error:
                    logger.warning(f"发送警告时出错 (尝试 {attempt + 1}/{max_retries}): {send_error}")
                    if attempt < max_retries - 1:
                        # 尝试重新激活群聊窗口
                        self._reactivate_chat_window()
                        time.sleep(2)
            # 所有重试都失败了
            logger.error(f"发送违规警告最终失败，已重试{max_retries}次: {warning_message}")
            # 记录发送失败的警告到日志文件
            self._log_failed_warning(record, warning_message)
        except Exception as e:
            logger.error(f"发送违规警告出错: {e}")

    def _log_failed_warning(self, record: ViolationRecord, warning_message: str):
        """
        记录发送失败的警告消息
        Args:
            record: 违规记录
            warning_message: 警告消息
        """
        try:
            # 创建失败日志目录
            log_dir = os.path.join(self.data_storage.storage_path if self.data_storage else "./monitor_data", "failed_warnings")
            os.makedirs(log_dir, exist_ok=True)
            # 记录到失败日志文件
            log_file = os.path.join(log_dir, f"failed_warnings_{datetime.now().strftime('%Y%m%d')}.txt")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 发送失败\n")
                f.write(f"用户: {record.user_name}\n")
                f.write(f"违规类型: {record.violation_type}\n")
                f.write(f"违规内容: {record.violation_content}\n")
                f.write(f"警告消息: {warning_message}\n")
                f.write("-" * 50 + "\n")
            logger.info(f"发送失败的警告已记录到: {log_file}")
        except Exception as e:
            logger.error(f"记录失败警告时出错: {e}")

    def set_violation_warning_template(self, template: str):
        """
        设置违规警告模板
        Args:
            template: 警告模板，使用{user}作为用户名占位符
        """
        self.violation_warning_template = template
        logger.info(f"违规警告模板已更新: {template}")

    def set_violation_warning_enabled(self, enabled: bool):
        """
        设置违规警告开关
        Args:
            enabled: 是否启用违规警告
        """
        self.enable_violation_warning = enabled
        status = "启用" if enabled else "禁用"
        logger.info(f"违规警告功能已{status}")

    def _send_message_to_current_chat(self, message: str) -> bool:
        """
        向当前聊天窗口发送消息 - 改进版，支持群名变更后的消息发送
        Args:
            message: 要发送的消息
        Returns:
            bool: 发送是否成功
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试发送消息 (第{attempt + 1}次): {message[:50]}...")
                
                # 确保当前群聊窗口是激活的
                if self.current_group:
                    logger.debug(f"确保群聊窗口激活: {self.current_group}")
                    result = self.wx.ChatWith(self.current_group)
                    if not result:
                        logger.warning(f"无法激活群聊窗口: {self.current_group}")
                        # 尝试重新激活聊天窗口
                        if not self._reactivate_chat_window():
                            logger.warning("重新激活聊天窗口失败")
                            if attempt < max_retries - 1:
                                time.sleep(2)
                                continue
                            else:
                                return False
                
                # 方法1: 使用WeChat原生SendMsg方法
                try:
                    self.wx.SendMsg(message)
                    logger.debug(f"WeChat SendMsg调用完成，等待验证...")
                    
                    # 等待消息发送并验证是否成功
                    time.sleep(2)
                    if self._verify_message_actually_sent(message):
                        logger.info(f"✅ 通过WeChat SendMsg发送消息成功: {message[:50]}...")
                        return True
                    else:
                        logger.debug(f"WeChat SendMsg验证失败，消息未实际发送")
                        
                except Exception as e1:
                    logger.debug(f"WeChat SendMsg方法失败: {e1}")
                
                # 方法2: 使用PyOfficeRobot API
                try:
                    import PyOfficeRobot
                    target_group = self.current_group if self.current_group else "test_demo1"
                    result = PyOfficeRobot.chat.send_message(who=target_group, message=message)
                    
                    # 等待消息发送并验证是否成功
                    time.sleep(2)
                    if self._verify_message_actually_sent(message):
                        logger.info(f"✅ 通过PyOfficeRobot API发送消息成功: {message[:50]}...")
                        return True
                    else:
                        logger.debug(f"PyOfficeRobot API验证失败，消息未实际发送")
                        
                except Exception as e2:
                    logger.debug(f"PyOfficeRobot API方法失败: {e2}")
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    logger.info(f"等待2秒后重试...")
                    time.sleep(2)
                    # 尝试重新激活聊天窗口
                    self._reactivate_chat_window()
                
            except Exception as e:
                logger.error(f"发送消息出错 (第{attempt + 1}次尝试): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
        
        logger.error(f"❌ 消息发送失败，已尝试 {max_retries} 次: {message[:50]}...")
        return False

    def _verify_message_actually_sent(self, message: str) -> bool:
        """
        验证消息是否实际发送成功（基于QA handler的实现）
        
        Args:
            message: 发送的消息内容
            
        Returns:
            bool: 消息是否实际发送成功
        """
        try:
            logger.debug("🔍 验证消息是否实际发送成功...")
            
            # 等待一段时间让消息出现在群聊中
            time.sleep(1)
            
            # 使用WeChat客户端读取最新消息
            if self.wx:
                try:
                    # 获取最新的几条消息
                    all_messages = self.wx.GetAllMessage
                    if not all_messages:
                        logger.debug("无法获取群聊消息进行验证")
                        return False
                    
                    # 检查最近的3条消息中是否包含我们发送的消息
                    recent_messages = all_messages[-3:] if len(all_messages) >= 3 else all_messages
                    
                    for raw_msg in recent_messages:
                        try:
                            sender, content, msg_id = raw_msg
                            
                            # 检查是否是我们发送的消息
                            if self.my_username and sender == self.my_username:
                                # 简单的内容匹配检查
                                message_clean = message.replace('\n', ' ').replace('\r', ' ').strip()
                                content_clean = content.replace('\n', ' ').replace('\r', ' ').strip()
                                
                                # 检查关键内容是否匹配（包含检查）
                                if len(message_clean) > 20 and message_clean[:20] in content_clean:
                                    logger.debug(f"✅ 验证成功：找到匹配的消息")
                                    return True
                                elif len(content_clean) > 20 and content_clean[:20] in message_clean:
                                    logger.debug(f"✅ 验证成功：找到匹配的消息")
                                    return True
                                # 检查完整消息匹配
                                elif self._messages_match(message_clean, content_clean):
                                    logger.debug(f"✅ 验证成功：完整消息匹配")
                                    return True
                                    
                        except Exception as e:
                            logger.debug(f"解析消息时出错: {e}")
                            continue
                    
                    logger.debug("❌ 验证失败：未找到匹配的消息")
                    return False
                    
                except Exception as e:
                    logger.debug(f"验证消息时出错: {e}")
                    return False
            else:
                logger.debug("WeChat客户端不可用，无法验证消息")
                return False
                
        except Exception as e:
            logger.debug(f"验证消息发送状态时出错: {e}")
            return False

    def _messages_match(self, sent_message: str, received_message: str) -> bool:
        """
        检查发送的消息和接收到的消息是否匹配（基于QA handler的实现）
        
        Args:
            sent_message: 发送的消息
            received_message: 接收到的消息
            
        Returns:
            bool: 消息是否匹配
        """
        try:
            # 清理消息内容
            sent_clean = ' '.join(sent_message.split())
            received_clean = ' '.join(received_message.split())
            
            # 完全匹配
            if sent_clean == received_clean:
                return True
            
            # 检查核心内容是否匹配（去除@用户名部分）
            sent_core = sent_clean
            received_core = received_clean
            
            # 移除@用户名部分进行比较
            import re
            sent_core = re.sub(r'@\w+\s*', '', sent_core).strip()
            received_core = re.sub(r'@\w+\s*', '', received_core).strip()
            
            # 检查核心内容是否匹配
            if sent_core in received_core or received_core in sent_core:
                return True
            
            # 检查关键词匹配（至少包含3个相同的关键词）
            sent_words = set(sent_core.split())
            received_words = set(received_core.split())
            common_words = sent_words.intersection(received_words)
            
            if len(common_words) >= 3 and len(sent_words) > 0:
                match_ratio = len(common_words) / len(sent_words)
                if match_ratio >= 0.6:  # 60%的词匹配
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"消息匹配检查出错: {e}")
            return False
