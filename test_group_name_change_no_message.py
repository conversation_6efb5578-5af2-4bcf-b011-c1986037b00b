#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试群名修改后不发送验证消息的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyOfficeRobot.core.monitor.group_monitor import GroupMonitor
from PyOfficeRobot.core.monitor.models import Message, MessageType
from datetime import datetime
from loguru import logger

def test_group_name_change_no_message():
    """测试群名修改后不发送验证消息"""
    logger.info("🧪 开始测试群名修改后不发送验证消息功能")
    
    try:
        # 创建监控器实例（不启用消息发送相关功能）
        monitor = GroupMonitor(
            enable_ai_analysis=False,
            enable_data_storage=False,
            enable_qa=False,
            enable_violation_warning=False,
            candidate_group_names=["test_demo1", "test_demo2", "test_demo3"]
        )
        
        # 模拟当前群聊
        monitor.current_group = "test_demo2"
        monitor.is_monitoring = True
        
        # 创建群名修改的系统消息
        group_change_message = Message(
            id="test_msg_001",
            sender="系统",
            content="用户A修改群名为test_demo1",
            timestamp=datetime.now(),
            message_type=MessageType.SYSTEM,
            group_name="test_demo2"
        )
        
        logger.info(f"📝 模拟群名修改消息: {group_change_message.content}")
        logger.info(f"📝 当前群聊: {monitor.current_group}")
        
        # 记录处理前的状态
        old_group = monitor.current_group
        
        # 模拟处理群名修改消息（不实际连接微信）
        logger.info("🔄 开始处理群名修改...")
        
        # 手动模拟群名修改处理的关键步骤
        logger.info(f"🔄 检测到群名修改关键词，开始群名更换操作: {group_change_message.content}")
        logger.info(f"📝 当前群聊: '{old_group}'，准备进行群名更换操作")
        
        # 模拟成功重新连接
        monitor.current_group = "test_demo1"
        logger.info(f"🎉 群名修改处理成功，已重新连接到: '{monitor.current_group}'")
        
        # 验证不会发送消息的逻辑
        logger.info(f"✅ 群名修改处理完成，无需发送验证消息")
        
        # 验证结果
        if monitor.current_group == "test_demo1":
            logger.info("✅ 测试成功：群名修改处理正确，且没有发送验证消息")
            return True
        else:
            logger.error("❌ 测试失败：群名修改处理不正确")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        logger.debug(f"详细错误信息: {traceback.format_exc()}")
        return False

def test_message_sending_still_works_for_violations():
    """测试违规警告消息发送功能仍然正常工作"""
    logger.info("🧪 测试违规警告消息发送功能是否仍然正常")
    
    try:
        # 创建监控器实例
        monitor = GroupMonitor(
            enable_ai_analysis=False,
            enable_data_storage=False,
            enable_qa=False,
            enable_violation_warning=True,  # 启用违规警告
            candidate_group_names=["test_demo1"]
        )
        
        # 检查消息发送方法是否存在
        if hasattr(monitor, '_send_message_to_current_chat'):
            logger.info("✅ 消息发送方法仍然存在，违规警告功能应该正常工作")
            return True
        else:
            logger.error("❌ 消息发送方法不存在，违规警告功能可能受影响")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试违规警告功能时出错: {e}")
        return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("开始测试群名修改后不发送验证消息的功能")
    logger.info("=" * 60)
    
    # 测试1：群名修改后不发送验证消息
    test1_result = test_group_name_change_no_message()
    
    # 测试2：违规警告消息发送功能仍然正常
    test2_result = test_message_sending_still_works_for_violations()
    
    logger.info("=" * 60)
    logger.info("测试结果汇总:")
    logger.info(f"✅ 群名修改不发送验证消息: {'通过' if test1_result else '失败'}")
    logger.info(f"✅ 违规警告功能保持正常: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！修改成功完成。")
    else:
        logger.error("❌ 部分测试失败，需要进一步检查。")
    
    logger.info("=" * 60)