[2025-07-30 11:34:51] 发送失败
用户: 凡梦
违规类型: 要求加好友、私聊
违规内容: 加我好友
警告消息: @凡梦 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。
--------------------------------------------------
[2025-07-30 12:20:29] 发送失败
用户: 凡梦
违规类型: 要求加好友、私聊
违规内容: 加我微信好友
警告消息: @凡梦 警告：本群禁止私自添加好友，违规会直接踢出群聊！请遵守群规。
--------------------------------------------------
[2025-07-30 19:56:33] 发送失败: @凡梦 Transformer是2017年提出的神经网络架构，核心是自注意力机制，能并行处理序列数据。它彻底改变了NLP领域，是BERT、GPT等模型的基础。相比RNN，它能更好地捕捉长距离依赖关系，训练效率更高。
[2025-07-30 20:16:40] 发送失败: @凡梦 RNN是循环神经网络，专为处理序列数据设计。它具有"记忆"功能，能处理变长输入，神经元会接收前一步输出作为当前输入，形成循环连接。在NLP、语音识别等领域应用广泛，能捕捉序列中的时序依赖关系。
