# 设计文档

## 概述

微信群聊监控机器人是基于PyOfficeRobot框架的扩展功能，旨在提供实时群聊监控、智能内容分析、违规检测和统计分析能力。系统采用模块化设计，集成智谱AI API进行内容分析，支持多种数据输出格式和灵活的配置选项。

## 架构

### 系统架构图

```mermaid
graph TB
    A[群聊监控主控制器] --> B[消息监听模块]
    A --> C[配置管理模块]
    A --> D[数据存储模块]
    
    B --> E[实时消息捕获]
    B --> F[消息解析器]
    
    F --> G[智谱AI分析模块]
    F --> H[关键词检测模块]
    F --> I[统计分析模块]
    
    G --> J[文本内容分析]
    G --> K[图片二维码检测]
    
    D --> L[Excel数据导出]
    D --> M[违规记录存储]
    D --> N[统计报告生成]
    
    O[PyOfficeRobot核心] --> B
    P[智谱AI API] --> G
```

### 核心组件

1. **群聊监控主控制器 (GroupMonitorController)**
   - 负责整个监控流程的协调和控制
   - 管理监控状态和生命周期
   - 处理异常和错误恢复

2. **消息监听模块 (MessageListener)**
   - 基于PyOfficeRobot的WeChat类实现
   - 实时监听群聊消息变化
   - 解析消息类型和内容

3. **智谱AI分析模块 (ZhipuAnalyzer)**
   - 集成智谱AI API进行内容分析
   - 支持文本违规检测和图片二维码识别
   - 提供分析结果缓存机制

4. **数据存储模块 (DataStorage)**
   - 管理消息数据的持久化存储
   - 支持Excel、CSV等多种格式
   - 提供数据查询和统计功能

## 组件和接口

### 1. 群聊监控主控制器

```python
class GroupMonitorController:
    def __init__(self, config: MonitorConfig):
        """初始化监控控制器"""
        
    def start_monitoring(self, group_name: str) -> bool:
        """启动群聊监控"""
        
    def stop_monitoring(self) -> bool:
        """停止群聊监控"""
        
    def get_monitoring_status(self) -> MonitorStatus:
        """获取监控状态"""
        
    def handle_new_message(self, message: Message) -> None:
        """处理新消息"""
```

### 2. 消息监听模块

```python
class MessageListener:
    def __init__(self, wx_client: WeChat):
        """初始化消息监听器"""
        
    def start_listening(self, group_name: str) -> None:
        """开始监听指定群聊"""
        
    def get_latest_messages(self) -> List[Message]:
        """获取最新消息列表"""
        
    def parse_message(self, raw_message: tuple) -> Message:
        """解析原始消息数据"""
```

### 3. 智谱AI分析模块

```python
class ZhipuAnalyzer:
    def __init__(self, api_key: str):
        """初始化智谱AI分析器"""
        
    def analyze_text_content(self, text: str) -> AnalysisResult:
        """分析文本内容是否违规"""
        
    def analyze_image_content(self, image_path: str) -> AnalysisResult:
        """分析图片内容是否包含二维码"""
        
    def is_violation_detected(self, result: AnalysisResult) -> bool:
        """判断是否检测到违规内容"""
```

### 4. 数据存储模块

```python
class DataStorage:
    def __init__(self, storage_config: StorageConfig):
        """初始化数据存储"""
        
    def save_message(self, message: Message) -> bool:
        """保存消息数据"""
        
    def save_violation_record(self, violation: ViolationRecord) -> bool:
        """保存违规记录"""
        
    def generate_statistics_report(self, time_range: TimeRange) -> StatisticsReport:
        """生成统计报告"""
        
    def export_to_excel(self, data: List[dict], file_path: str) -> bool:
        """导出数据到Excel文件"""
```

## 数据模型

### 消息数据模型

```python
@dataclass
class Message:
    id: str                    # 消息唯一标识
    sender: str               # 发送者昵称
    content: str              # 消息内容
    message_type: MessageType # 消息类型（文本/图片/文件等）
    timestamp: datetime       # 发送时间
    group_name: str          # 群聊名称
    is_analyzed: bool        # 是否已分析
    analysis_result: Optional[AnalysisResult] = None

@dataclass
class AnalysisResult:
    is_violation: bool        # 是否违规
    violation_type: str       # 违规类型
    confidence: float         # 置信度
    detected_keywords: List[str] # 检测到的关键词
    analysis_time: datetime   # 分析时间

@dataclass
class ViolationRecord:
    user_id: str             # 用户标识
    user_name: str           # 用户昵称
    violation_type: str      # 违规类型
    violation_content: str   # 违规内容
    detection_time: datetime # 检测时间
    group_name: str         # 群聊名称
    confidence: float       # 置信度
```

### 配置数据模型

```python
@dataclass
class MonitorConfig:
    target_groups: List[str]           # 目标群聊列表
    zhipu_api_key: str                # 智谱AI API密钥
    storage_path: str                 # 数据存储路径
    keywords: List[str]               # 自定义关键词
    violation_keywords: List[str]     # 违规关键词列表
    enable_ai_analysis: bool          # 是否启用AI分析
    enable_image_analysis: bool       # 是否启用图片分析
    monitoring_interval: int          # 监控间隔（秒）
    max_message_cache: int           # 最大消息缓存数量
```

## 错误处理

### 错误类型和处理策略

1. **网络连接错误**
   - 智谱AI API调用失败：重试机制，最多重试3次
   - 微信连接中断：自动重连，记录错误日志

2. **数据处理错误**
   - 文件写入失败：切换到备用存储路径
   - 数据格式错误：记录错误并跳过该条消息

3. **系统资源错误**
   - 内存不足：清理过期缓存数据
   - 磁盘空间不足：压缩历史数据文件

### 错误恢复机制

```python
class ErrorHandler:
    def __init__(self, max_retries: int = 3):
        self.max_retries = max_retries
        
    def handle_api_error(self, error: Exception, retry_count: int) -> bool:
        """处理API调用错误"""
        
    def handle_storage_error(self, error: Exception) -> bool:
        """处理存储错误"""
        
    def handle_connection_error(self, error: Exception) -> bool:
        """处理连接错误"""
```

## 测试策略

### 单元测试

1. **消息解析测试**
   - 测试不同类型消息的解析准确性
   - 测试边界情况和异常数据

2. **AI分析测试**
   - 模拟智谱AI API响应
   - 测试违规检测的准确性

3. **数据存储测试**
   - 测试Excel文件生成和数据完整性
   - 测试并发写入场景

### 集成测试

1. **端到端监控测试**
   - 模拟完整的群聊监控流程
   - 验证数据流的完整性

2. **错误恢复测试**
   - 模拟各种错误场景
   - 验证系统的自动恢复能力

### 性能测试

1. **消息处理性能**
   - 测试高频消息场景下的处理能力
   - 监控内存使用和CPU占用

2. **AI分析性能**
   - 测试智谱AI API的响应时间
   - 优化批量分析的效率

## 部署和配置

### 配置文件示例

```json
{
  "monitor_config": {
    "target_groups": ["测试群", "工作群"],
    "zhipu_api_key": "your_api_key_here",
    "storage_path": "./monitor_data",
    "keywords": ["报名", "学习", "课程"],
    "violation_keywords": ["加好友", "转账", "加群", "加我好友"],
    "enable_ai_analysis": true,
    "enable_image_analysis": true,
    "monitoring_interval": 3,
    "max_message_cache": 1000
  },
  "storage_config": {
    "message_file": "messages.xlsx",
    "violation_file": "violations.xlsx",
    "statistics_file": "statistics.xlsx",
    "backup_enabled": true,
    "backup_interval": 3600
  },
  "notification_config": {
    "enable_console_output": true,
    "enable_file_logging": true,
    "enable_sound_alert": false,
    "log_level": "INFO"
  }
}
```

### 依赖项

- PyOfficeRobot >= 0.1.26
- poai >= 0.0.11 (智谱AI集成)
- pandas >= 2.2.2 (数据处理)
- openpyxl (Excel文件操作)
- loguru (日志记录)
- schedule (定时任务)

### 安装和启动

```bash
# 安装依赖
pip install -r requirements.txt

# 配置文件
cp config.example.json config.json
# 编辑config.json设置API密钥和监控参数

# 启动监控
python -m PyOfficeRobot.monitor.group_monitor --config config.json
```