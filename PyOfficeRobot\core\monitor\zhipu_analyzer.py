# -*- coding: UTF-8 -*-
"""
智谱AI分析模块
"""

import time
from typing import List, Optional
from datetime import datetime
from loguru import logger

try:
    from zhipuai import ZhipuAI
except ImportError:
    logger.warning("zhipuai库未安装，智谱AI功能将不可用。请运行: pip install zhipuai")
    ZhipuAI = None


class AnalysisResult:
    """分析结果数据类"""
    
    def __init__(self, is_violation: bool = False, violation_type: str = "", 
                 confidence: float = 0.0, detected_keywords: List[str] = None,
                 analysis_time: datetime = None):
        self.is_violation = is_violation
        self.violation_type = violation_type
        self.confidence = confidence
        self.detected_keywords = detected_keywords or []
        self.analysis_time = analysis_time or datetime.now()


class ZhipuAnalyzer:
    """智谱AI分析器"""
    
    # 预定义的违规关键词
    VIOLATION_KEYWORDS = [
        "加好友", "转账", "加群", "加我好友", "微信号", "QQ号", 
        "联系方式", "私聊", "加微信", "扫码", "二维码"
    ]
    
    def __init__(self, api_key: str = None, max_retries: int = 3):
        """
        初始化智谱AI分析器
        
        Args:
            api_key: 智谱AI API密钥
            max_retries: 最大重试次数
        """
        self.api_key = api_key
        self.max_retries = max_retries
        self.client = None
        
        if api_key and ZhipuAI:
            try:
                self.client = ZhipuAI(api_key=api_key)
                logger.info("智谱AI客户端初始化成功")
            except Exception as e:
                logger.error(f"智谱AI客户端初始化失败: {e}")
        elif not ZhipuAI:
            logger.warning("智谱AI库未安装，将使用关键词匹配模式")
        else:
            logger.warning("未提供API密钥，将使用关键词匹配模式")
    
    def analyze_text_content(self, text: str) -> AnalysisResult:
        """
        分析文本内容是否违规
        
        Args:
            text: 要分析的文本内容
            
        Returns:
            AnalysisResult: 分析结果
        """
        if not text or not text.strip():
            return AnalysisResult()
        
        # 首先进行关键词匹配
        keyword_result = self._analyze_by_keywords(text)
        
        # 如果有API客户端，进行AI分析
        if self.client:
            try:
                ai_result = self._analyze_by_ai(text)
                # 合并结果，以更严格的结果为准
                if ai_result.is_violation or keyword_result.is_violation:
                    return AnalysisResult(
                        is_violation=True,
                        violation_type=ai_result.violation_type or keyword_result.violation_type,
                        confidence=max(ai_result.confidence, keyword_result.confidence),
                        detected_keywords=list(set(ai_result.detected_keywords + keyword_result.detected_keywords))
                    )
            except Exception as e:
                logger.error(f"AI分析失败，使用关键词分析结果: {e}")
        
        return keyword_result
    
    def analyze_image_content(self, image_path: str) -> AnalysisResult:
        """
        分析图片内容是否包含二维码
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            AnalysisResult: 分析结果
        """
        if not self.client:
            logger.warning("无AI客户端，无法分析图片内容")
            return AnalysisResult()
        
        try:
            # 使用智谱AI视觉模型分析图片
            response = self.client.chat.completions.create(
                model="glm-4v",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请分析这张图片是否包含二维码。如果包含二维码，请回答'是'，否则回答'否'。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_path
                                }
                            }
                        ]
                    }
                ]
            )
            
            result_text = response.choices[0].message.content.strip()
            is_violation = "是" in result_text or "二维码" in result_text
            
            return AnalysisResult(
                is_violation=is_violation,
                violation_type="图片二维码" if is_violation else "",
                confidence=0.8 if is_violation else 0.2,
                detected_keywords=["二维码"] if is_violation else []
            )
            
        except Exception as e:
            logger.error(f"图片分析失败: {e}")
            return AnalysisResult()
    
    def _analyze_by_keywords(self, text: str) -> AnalysisResult:
        """
        基于关键词的违规检测
        
        Args:
            text: 要分析的文本
            
        Returns:
            AnalysisResult: 分析结果
        """
        detected_keywords = []
        
        for keyword in self.VIOLATION_KEYWORDS:
            if keyword in text:
                detected_keywords.append(keyword)
        
        if detected_keywords:
            return AnalysisResult(
                is_violation=True,
                violation_type="关键词违规",
                confidence=0.9,
                detected_keywords=detected_keywords
            )
        
        return AnalysisResult()
    
    def _analyze_by_ai(self, text: str) -> AnalysisResult:
        """
        基于AI的违规检测
        
        Args:
            text: 要分析的文本
            
        Returns:
            AnalysisResult: 分析结果
        """
        if not self.client:
            return AnalysisResult()
        
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                prompt = f"""
请分析以下群聊消息是否包含违规内容。违规内容包括但不限于：
1. 要求加好友、私聊
2. 要求转账、付款
3. 推广其他群聊
4. 分享联系方式（微信号、QQ号等）
5. 要求扫码或分享二维码

消息内容："{text}"

请回答格式：
违规：是/否
类型：[如果违规，说明违规类型]
置信度：[0-1之间的数字]
"""

                response = self.client.chat.completions.create(
                    model="glm-4",
                    messages=[
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1
                )
                
                result_text = response.choices[0].message.content.strip()
                return self._parse_ai_response(result_text)
                
            except Exception as e:
                retry_count += 1
                logger.warning(f"AI分析重试 {retry_count}/{self.max_retries}: {e}")
                if retry_count < self.max_retries:
                    time.sleep(1)  # 等待1秒后重试
        
        logger.error("AI分析达到最大重试次数，返回默认结果")
        return AnalysisResult()
    
    def _parse_ai_response(self, response_text: str) -> AnalysisResult:
        """
        解析AI响应结果
        
        Args:
            response_text: AI返回的文本
            
        Returns:
            AnalysisResult: 解析后的结果
        """
        try:
            lines = response_text.split('\n')
            is_violation = False
            violation_type = ""
            confidence = 0.0
            
            for line in lines:
                line = line.strip()
                if line.startswith('违规：'):
                    is_violation = '是' in line
                elif line.startswith('类型：'):
                    violation_type = line.replace('类型：', '').strip()
                elif line.startswith('置信度：'):
                    try:
                        confidence_str = line.replace('置信度：', '').strip()
                        confidence = float(confidence_str)
                    except ValueError:
                        confidence = 0.5
            
            return AnalysisResult(
                is_violation=is_violation,
                violation_type=violation_type,
                confidence=confidence,
                detected_keywords=[]
            )
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return AnalysisResult()
    
    def is_violation_detected(self, result: AnalysisResult) -> bool:
        """
        判断是否检测到违规内容
        
        Args:
            result: 分析结果
            
        Returns:
            bool: 是否违规
        """
        return result.is_violation